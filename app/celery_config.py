"""
Celery configuration and task registration
"""
from app.celery_app import celery_app

# Register tasks with specific names
@celery_app.task(
    name='app.tasks.mcc_task.execute_mcc_task',
    bind=True,
    time_limit= 180,  # 3 minutes hard timeout
    soft_time_limit=120,  # 2 minutes soft timeout (sends exception before hard timeout)
    max_retries=3,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=60,  # Maximum backoff time (1 minute)
    retry_jitter=True,  # Add random jitter to retry delay
    rate_limit='4/m',  # Rate limit: 4 tasks per minute
    autoretry_for=(Exception,),  # Auto retry on exceptions
    acks_late=True,  # Task acknowledgment after execution (ensures task is re-queued if worker crashes)
    track_started=True  # Track when task is started
)
def execute_mcc_task_wrapper(self, website_url, scrape_request_ref_id, org_id):
    """
    DEPRECATED: Use app.tasks.mcc_task.execute_mcc_task_new instead
    
    This task is maintained for backward compatibility with existing code.
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        org_id: Organization ID
        
    Returns:
        Results from the MCC analysis task
    """
    # We'll import this function only when it's called to avoid circular imports
    try:
        from app.tasks.celery_tasks import execute_mcc_task
        return execute_mcc_task(website_url, scrape_request_ref_id, org_id)
    except Exception as exc:
        self.retry(exc=exc, countdown=60 * (2 ** (self.request.retries)))  # Exponential backoff 

@celery_app.task(
    name='app.tasks.mcc_task.execute_mcc_task_new',
    bind=True,
    time_limit= 180,  # 3 minutes hard timeout
    soft_time_limit=120,  # 2 minutes soft timeout (sends exception before hard timeout)
    max_retries=3,  # Maximum number of retries
    retry_backoff=True,  # Exponential backoff for retries
    retry_backoff_max=60,  # Maximum backoff time (1 minute)
    retry_jitter=True,  # Add random jitter to retry delay
    rate_limit='4/m',  # Rate limit: 4 tasks per minute
    autoretry_for=(Exception,),  # Auto retry on exceptions
    acks_late=True,  # Task acknowledgment after execution (ensures task is re-queued if worker crashes)
    track_started=True  # Track when task is started
)
def execute_mcc_task_new_wrapper(self, website_url, scrape_request_ref_id, org_id, **kwargs):
    """
    Wrapper function for the modular MCC analysis task
    
    Args:
        self: Task instance (added with bind=True)
        website_url: URL of the website to analyze
        scrape_request_ref_id: Reference ID for the scrape request
        org_id: Organization ID
        **kwargs: Additional parameters
        
    Returns:
        Results from the MCC analysis task
    """
    try:
        from app.tasks.celery_tasks import execute_mcc_task_new
        return execute_mcc_task_new(website_url, scrape_request_ref_id, org_id, **kwargs)
    except Exception as exc:
        self.retry(exc=exc, countdown=60 * (2 ** (self.request.retries)))  # Exponential backoff

# Import the test task
from app.tasks.test_task import test_task 
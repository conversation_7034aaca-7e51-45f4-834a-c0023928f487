import os
from typing import Optional

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv(".env")


class Settings(BaseSettings):
    # Core application settings
    APP_NAME: str = "Entity Extraction API"
    DEBUG: bool = True

    # Database configuration (required)
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./entity_extraction.db")

    # AI API keys (required for entity extraction)
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    GEMINI_API_KEY: Optional[str] = os.getenv("GEMINI_API_KEY")

    # Optional OpenAI configuration
    OPENAI_DEPLOYMENT_NAME: Optional[str] = os.getenv("OPENAI_DEPLOYMENT_NAME")

    # Optional Azure OpenAI configuration (if using Azure)
    AZURE_API_TYPE: Optional[str] = os.getenv("AZURE_API_TYPE")
    AZURE_API_BASE: Optional[str] = os.getenv("AZURE_API_BASE")
    AZURE_API_VERSION: Optional[str] = os.getenv("AZURE_API_VERSION")
    AZURE_API_KEY: Optional[str] = os.getenv("AZURE_API_KEY")
    AZURE_DEPLOYMENT_NAME: Optional[str] = os.getenv("AZURE_DEPLOYMENT_NAME")

    # Optional proxy configuration
    WEBSHARE_PROXY_USERNAME: Optional[str] = os.getenv("WEBSHARE_PROXY_USERNAME", "")
    WEBSHARE_PROXY_PASSWORD: Optional[str] = os.getenv("WEBSHARE_PROXY_PASSWORD", "")

    # Other service configurations
    BASE_URL: Optional[str] = os.getenv("BASE_URL")
    ADMIN_USERNAME: Optional[str] = os.getenv("ADMIN_USERNAME")
    ADMIN_PASSWORD: Optional[str] = os.getenv("ADMIN_PASSWORD")
    INPUT_PATH: Optional[str] = os.getenv("INPUT_PATH")
    OUTPUT_PATH: Optional[str] = os.getenv("OUTPUT_PATH")
    BIZTEL_API_KEY: Optional[str] = os.getenv("BIZTEL_API_KEY")

    # Email configuration for error notifications
    SMTP_SERVER: Optional[str] = os.getenv("SMTP_SERVER", "smtp.gmail.com")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", 587))
    SMTP_USERNAME: Optional[str] = os.getenv("SMTP_USERNAME", "")
    SMTP_PASSWORD: Optional[str] = os.getenv("SMTP_PASSWORD", "")
    ERROR_NOTIFICATION_EMAIL: Optional[str] = os.getenv("ERROR_NOTIFICATION_EMAIL", "<EMAIL>")
    EMAIL_FROM_NAME: Optional[str] = os.getenv("EMAIL_FROM_NAME", "WebReview DS API")


    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignore extra fields instead of raising validation errors


settings = Settings()

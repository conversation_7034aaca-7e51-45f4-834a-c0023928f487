from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlmodel import Session, select
import json
import traceback
import logging
import time
from datetime import datetime

from app.database import get_session
from app.models.db_models import MccAnalysis, WebsiteUrls, ScrapeRequestTracker
from app.models.request_models import MccAnalysisRequest
from app.services.mcc_service import MccClassificationService
from app.utils.website_url_processor import store_urls_from_request, get_urls_by_scrape_ref
from app.utils.logger import ConsoleLogger
from app.utils.mcc_server_logger import get_mcc_logger

router = APIRouter()
logger = logging.getLogger(__name__)


def process_mcc_analysis_direct(analysis_id: int):
    """
    Process MCC analysis directly without Celery

    Args:
        analysis_id (int): The ID of the MccAnalysis record to process

    Returns:
        dict: Result of the analysis with status and details
    """

    console_logger = ConsoleLogger(analysis_id)
    mcc_server_logger = get_mcc_logger()
    console_logger.info(f"Starting MCC analysis for analysis_id: {analysis_id}")

    start_time = time.time()

    try:
        # Get the MccAnalysis record from the database
        from sqlmodel import Session, select
        from app.models.db_models import MccAnalysis
        from app.database import engine

        console_logger.info(f"Importing required modules for MCC analysis")

        console_logger.info(f"Opening database session to get MccAnalysis record")
        with Session(engine) as session:
            # Get the MccAnalysis record
            analysis = session.get(MccAnalysis, analysis_id)
            if not analysis:
                error_msg = f"MccAnalysis with ID {analysis_id} not found"
                console_logger.error(error_msg)
                raise ValueError(error_msg)

            # Extract values while object is still attached to session
            scrape_request_ref_id = analysis.scrape_request_ref_id
            org_id = analysis.org_id
            website = analysis.website

            console_logger.info(f"Found MccAnalysis record: {website}, ref_id: {scrape_request_ref_id}")

            # Update analysis status
            analysis.processing_status = "PROCESSING"
            analysis.started_at = datetime.now().isoformat() + "Z"
            session.commit()
            console_logger.info(f"Updated MccAnalysis status to PROCESSING")

        # Initialize the MCC classification service using extracted values
        console_logger.info(f"Initializing MCC classification service")
        mcc_service = MccClassificationService(
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )

        # Process the MCC analysis
        console_logger.info(f"Running MCC analysis")
        result = mcc_service.process_mcc_analysis()

        processing_time = time.time() - start_time
        console_logger.info(f"MCC analysis completed in {processing_time:.2f} seconds with status: {result.get('status')}")

        # Update the analysis status with comprehensive error handling
        try:
            console_logger.info(f"Updating MccAnalysis status to {result.get('status', 'UNKNOWN')}")
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    # Safely update status
                    analysis.processing_status = "COMPLETED" if result.get("status") == "COMPLETED" else "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"

                    # Safely extract and update MCC information
                    if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                        mcc_code = result["mcc_info"].get("mcc")
                        if mcc_code is not None:
                            analysis.mcc_code = str(mcc_code) if not isinstance(mcc_code, str) else mcc_code

                        reasoning = result["mcc_info"].get("reason")
                        if reasoning:
                            analysis.reasoning = str(reasoning)

                        # Update business category from MCC info
                        business_category = result["mcc_info"].get("business_category")
                        if business_category:
                            analysis.business_category = str(business_category)

                    # Safely extract and update business description
                    if result.get("website_info") and isinstance(result["website_info"], dict):
                        business_desc = result["website_info"].get("business_description") or result["website_info"].get("website_description")
                        if business_desc:
                            analysis.business_description = str(business_desc)

                    session.commit()
                    console_logger.info(f"Successfully updated MccAnalysis status")
                else:
                    console_logger.warning(f"MccAnalysis with ID {analysis_id} not found for status update")
        except Exception as update_error:
            console_logger.error(f"Error updating MccAnalysis status: {str(update_error)}")
            # Don't fail the task if status update fails - the analysis was completed

        # Send MCC results webhook after successful analysis
        if result.get("status") == "COMPLETED":
            try:
                console_logger.info("📤 Preparing to send MCC results webhook")

                # Import webhook function
                from app.utils.webhook_utils import send_mcc_results_webhook

                # Prepare webhook data from analysis results
                mcc_webhook_data = {
                    "status": "COMPLETED"
                }

                # Extract MCC information
                if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                    mcc_webhook_data["mcc"] = result["mcc_info"].get("mcc", -1)
                    mcc_webhook_data["reason"] = result["mcc_info"].get("reason", "")

                # Extract business information
                if result.get("website_info") and isinstance(result["website_info"], dict):
                    business_desc = result["website_info"].get("business_description") or result["website_info"].get("website_description")
                    mcc_webhook_data["businessDescription"] = business_desc or ""

                # Get business category from MCC info
                business_category = ""
                if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                    business_category = result["mcc_info"].get("business_category", "")
                mcc_webhook_data["businessCategory"] = business_category

                # Send the webhook
                webhook_success = send_mcc_results_webhook(
                    scrape_request_ref_id=scrape_request_ref_id,
                    website=website,
                    mcc_result=mcc_webhook_data,
                    analysis_id=analysis_id,
                    logger=console_logger
                )

                if webhook_success:
                    console_logger.info("✅ MCC results webhook sent successfully")
                else:
                    console_logger.error("❌ Failed to send MCC results webhook")

            except Exception as webhook_error:
                console_logger.error(f"❌ Error sending MCC results webhook: {str(webhook_error)}")
                # Don't fail the task if webhook fails - the analysis was completed
        else:
            console_logger.info("⚠️ Skipping webhook send due to non-COMPLETED status", {"status": result.get("status")})

        # Add execution details to result
        result.update({
            "analysis_id": analysis_id,
            "execution_time": processing_time
        })

        return result

    except Exception as e:
        error_msg = f"Error in MCC analysis: {str(e)}"
        console_logger.error(error_msg, error=e)

        # Log the full traceback for debugging
        console_logger.error("Full traceback:", error=traceback.format_exc())

        processing_time = time.time() - start_time

        # Send comprehensive system monitoring notification
        try:
            from app.utils.email_notification_service import notify_system_issue

            system_context = {
                "analysis_id": analysis_id,
                "website": website if 'website' in locals() else "unknown",
                "scrape_request_ref_id": scrape_request_ref_id if 'scrape_request_ref_id' in locals() else "unknown",
                "processing_time_seconds": round(processing_time, 2),
                "error_traceback": traceback.format_exc()
            }

            console_logger.info("📧 Sending comprehensive system monitoring notification")
            notify_system_issue(
                issue_type="mcc_analysis_error",
                issue_message=f"MCC Analysis Failed: {error_msg}",
                component="mcc_analysis",
                severity="HIGH",
                additional_context=system_context,
                org_id="system"
            )

        except Exception as email_error:
            console_logger.error(f"❌ Failed to send system monitoring notification: {str(email_error)}")

        # Update the analysis status
        try:
            console_logger.info(f"Updating MccAnalysis status to FAILED after error")
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    analysis.processing_status = "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    analysis.error_message = error_msg
                    session.commit()
                    console_logger.info(f"Successfully updated MccAnalysis status to FAILED")

                    # Extract website for webhook
                    website = getattr(analysis, 'website', '')
                    scrape_request_ref_id = getattr(analysis, 'scrape_request_ref_id', '')

        except Exception as db_error:
            console_logger.error(f"Error updating analysis status: {str(db_error)}")
            website = ''
            scrape_request_ref_id = ''

        # Send FAILED status webhook
        if website and scrape_request_ref_id:
            try:
                console_logger.info("📤 Preparing to send FAILED status webhook")
                from app.utils.webhook_utils import send_mcc_results_webhook

                failed_webhook_data = {
                    "status": "FAILED",
                    "mcc": -1,
                    "businessCategory": "",
                    "businessDescription": "",
                    "error": error_msg
                }

                webhook_success = send_mcc_results_webhook(
                    scrape_request_ref_id=scrape_request_ref_id,
                    website=website,
                    mcc_result=failed_webhook_data,
                    analysis_id=analysis_id,
                    logger=console_logger
                )

                if webhook_success:
                    console_logger.info("✅ FAILED status webhook sent successfully")
                else:
                    console_logger.error("❌ Failed to send FAILED status webhook")

            except Exception as webhook_error:
                console_logger.error(f"❌ Error sending FAILED status webhook: {str(webhook_error)}")

        # Send error notification email (if configured for this org)
        try:
            from app.utils.email_notification_service import notify_analysis_error

            # Extract org_id from analysis or use default
            org_id = getattr(analysis, 'org_id', 'default') if 'analysis' in locals() else 'default'

            console_logger.info("📧 Attempting to send error notification email")
            email_sent = notify_analysis_error(
                org_id=org_id,
                analysis_type="mcc_analysis",
                analysis_id=analysis_id,
                website=website if website else "unknown",
                scrape_request_ref_id=scrape_request_ref_id if scrape_request_ref_id else "unknown",
                error_message=error_msg,
                processing_time=processing_time
            )

            if email_sent:
                console_logger.info("✅ Error notification email sent successfully")
            else:
                console_logger.info("ℹ️ Error notification email not sent (may not be configured for this org)")

        except Exception as email_error:
            console_logger.error(f"❌ Error sending notification email: {str(email_error)}")

        return {
            "status": "FAILED",
            "error": error_msg,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        }


@router.post("/")
def create_or_get_mcc_analysis(
    request: MccAnalysisRequest,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session)
):
    """
    Create a new MCC analysis or get existing one with comprehensive error handling

    This endpoint:
    1. Checks if an analysis with the same scrape request ID exists
    2. Creates a new MCC analysis record if needed
    3. Stores website URLs in the database
    4. Processes the analysis directly (no Celery) as a background task
    """
    try:
        # Validate request
        if not request or not hasattr(request, 'scrapeRequestRefID') or not request.scrapeRequestRefID:
            logger.error("Invalid request: missing scrapeRequestRefID")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: scrapeRequestRefID is required"
            )
        
        if not hasattr(request, 'website') or not request.website:
            logger.error("Invalid request: missing website")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: website is required"
            )
        
        logger.info(f"Processing MCC analysis request for {request.website} with ref_id {request.scrapeRequestRefID}")
        
        # Check if an MccAnalysis with the same scrapeRequestRefID exists
        try:
            statement = select(MccAnalysis).where(
                MccAnalysis.scrape_request_ref_id == request.scrapeRequestRefID
            )
            existing_analysis = session.exec(statement).first()
            
            if existing_analysis:
                logger.info(f"MCC analysis already exists with ID {existing_analysis.id}")
                return {
                    "message": "MccAnalysis already exists.", 
                    "id": existing_analysis.id,
                    "status": getattr(existing_analysis, 'processing_status', 'UNKNOWN')
                }
        except Exception as db_error:
            logger.error(f"Database error checking existing analysis: {str(db_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database error while checking existing analysis"
            )

        # Store URLs from the request first
        try:
            url_store_result = store_urls_from_request(request, session, auto_classify=False)
            if url_store_result != 1:  # 1 indicates success, -1 indicates failure
                logger.error("Failed to store URLs")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to store website URLs"
                )
        except Exception as url_store_error:
            logger.error(f"Error storing URLs: {str(url_store_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error storing URLs: {str(url_store_error)}"
            )
        
        # Create a new MccAnalysis from the request (no website_id needed)
        try:
            new_analysis = MccAnalysis(
                website=request.website,
                scrape_request_ref_id=request.scrapeRequestRefID,
                org_id=getattr(request, 'org_id', None) or "default",
                processing_status="PENDING"
            )
            session.add(new_analysis)
            session.commit()
            session.refresh(new_analysis)  # Refresh to get the auto-generated ID
            
            logger.info(f"Created new MCC analysis with ID {new_analysis.id}")
            
        except Exception as create_error:
            session.rollback()
            logger.error(f"Error creating MCC analysis record: {str(create_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating MCC analysis record: {str(create_error)}"
            )
        
        # Process MCC analysis as background task
        try:
            background_tasks.add_task(process_mcc_analysis_direct, new_analysis.id)
            logger.info(f"Added MCC analysis background task for analysis {new_analysis.id}")

        except Exception as task_error:
            logger.error(f"Error adding background task: {str(task_error)}")
            # Don't fail the request if task creation fails - analysis record is created

        return {
            "message": "MccAnalysis created successfully and processing started.",
            "id": new_analysis.id,
            "status": "PENDING"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_or_get_mcc_analysis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )



@router.get("/by-request/{scrape_request_ref_id}")
def get_mcc_analysis_by_request_id(
    scrape_request_ref_id: str, 
    session: Session = Depends(get_session)
):
    """
    Get MCC analysis by scrape request reference ID
    """
    try:
        analysis = session.exec(
            select(MccAnalysis).where(
                MccAnalysis.scrape_request_ref_id == scrape_request_ref_id
            )
        ).first()
        
        if not analysis:
            raise HTTPException(
                status_code=404, 
                detail=f"MccAnalysis not found for request ID: {scrape_request_ref_id}"
            )
        
        # Parse details JSON if available
        details = {}
        if hasattr(analysis, 'details') and analysis.details:
            try:
                details = json.loads(analysis.details)
            except json.JSONDecodeError:
                details = {"error": "Invalid JSON in details field"}
        
        # Get MCC code
        mcc_code = analysis.mcc_code if hasattr(analysis, 'mcc_code') else None
        
        # Prepare response with details
        response = {
            "id": analysis.id,
            "website": analysis.website,
            "scrape_request_ref_id": analysis.scrape_request_ref_id,
            "mcc_code": mcc_code,
            "business_description": analysis.business_description if hasattr(analysis, 'business_description') else "",
            "reasoning": analysis.reasoning if hasattr(analysis, 'reasoning') else "",
            "processing_status": analysis.processing_status if hasattr(analysis, 'processing_status') else "UNKNOWN",
            "created_at": analysis.created_at if hasattr(analysis, 'created_at') else None,
            "started_at": analysis.started_at if hasattr(analysis, 'started_at') else None,
            "completed_at": analysis.completed_at if hasattr(analysis, 'completed_at') else None
        }
        
        # Add details only if they exist
        if details:
            response["details"] = details
            
            # Extract flow indicators from details to top level for easier access
            if isinstance(details, dict):
                flow_indicators = [
                    "analysis_flow_type", "content_availability_status", 
                    "fallback_method_used", "text_extraction_used", "insufficient_data"
                ]
                for indicator in flow_indicators:
                    if indicator in details:
                        response[indicator] = details[indicator]
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MCC analysis by request ID {scrape_request_ref_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting MCC analysis: {str(e)}"
        )




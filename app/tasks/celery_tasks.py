"""
Celery tasks for MCC analysis processing and system monitoring
"""

import os
import time
import traceback
from datetime import datetime

from app.celery_app import celery_app
from app.utils.logger import ConsoleLogger
from app.services.mcc_service import MccClassificationService


@celery_app.task(bind=True, name="process_mcc_analysis")
def process_mcc_analysis(self, analysis_id: int):
    """
    Celery task to process MCC analysis
    
    Args:
        analysis_id (int): The ID of the MccAnalysis record to process
    
    Returns:
        dict: Result of the analysis with status and details
    """
    
    logger = ConsoleLogger(analysis_id)
    logger.info(f"Starting MCC analysis task for analysis_id: {analysis_id}")
    
    start_time = time.time()
    
    try:
        # Get the MccAnalysis record from the database
        from sqlmodel import Session, select
        from app.models.db_models import MccAnalysis
        from app.database import engine
        
        logger.info(f"Importing required modules for MCC analysis")
        
        # Import the service here to avoid circular import
        from app.services.mcc_service import MccClassificationService
        
        logger.info(f"Opening database session to get MccAnalysis record")
        with Session(engine) as session:
            # Get the MccAnalysis record
            analysis = session.get(MccAnalysis, analysis_id)
            if not analysis:
                error_msg = f"MccAnalysis with ID {analysis_id} not found"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # Extract values while object is still attached to session
            scrape_request_ref_id = analysis.scrape_request_ref_id
            org_id = analysis.org_id
            website = analysis.website

            logger.info(f"Found MccAnalysis record: {website}, ref_id: {scrape_request_ref_id}")

            #  COMMENTED OUT: Error simulations for testing monitoring (can be re-enabled for testing)
            # if scrape_request_ref_id == "test_database_error_monitoring":
            #     logger.info("🧪 TESTING: Simulating database connection error")
            #     raise Exception("Database connection failed: Connection timeout after 30 seconds (SIMULATED FOR TESTING)")
            # elif scrape_request_ref_id == "test_api_error_monitoring":
            #     logger.info("🧪 TESTING: Simulating Gemini API error")
            #     raise Exception("Gemini API error: Quota exceeded for requests per minute. Please try again later (SIMULATED FOR TESTING)")
            # elif scrape_request_ref_id == "test_network_error_monitoring":
            #     logger.info("🧪 TESTING: Simulating network connectivity error")
            #     raise Exception("Network error: Connection refused - DNS resolution failed for target host (SIMULATED FOR TESTING)")
            # elif scrape_request_ref_id == "test_resource_error_monitoring":
            #     logger.info("🧪 TESTING: Simulating system resource error")
            #     raise Exception("System resource error: Insufficient memory available for processing (SIMULATED FOR TESTING)")
            # elif scrape_request_ref_id == "test_celery_error_monitoring":
            #     logger.info("🧪 TESTING: Simulating Celery worker error")
            #     raise Exception("Celery worker error: Task execution failed due to worker overload (SIMULATED FOR TESTING)")

            # Update analysis status
            analysis.processing_status = "PROCESSING"
            analysis.started_at = datetime.now().isoformat() + "Z"
            session.commit()
            logger.info(f"Updated MccAnalysis status to PROCESSING")
        
        # Initialize the MCC classification service using extracted values
        logger.info(f"Initializing MCC classification service")
        mcc_service = MccClassificationService(
            scrape_request_ref_id=scrape_request_ref_id,
            org_id=org_id
        )
        
        # Process the MCC analysis
        logger.info(f"Running MCC analysis")
        result = mcc_service.process_mcc_analysis()
        
        processing_time = time.time() - start_time
        logger.info(f"MCC analysis task completed in {processing_time:.2f} seconds with status: {result.get('status')}")
        
        # Update the analysis status with comprehensive error handling
        try:
            logger.info(f"Updating MccAnalysis status to {result.get('status', 'UNKNOWN')}")
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    # Safely update status
                    analysis.processing_status = "COMPLETED" if result.get("status") == "COMPLETED" else "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    
                    # Safely extract and update MCC information
                    if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                        mcc_code = result["mcc_info"].get("mcc")
                        if mcc_code is not None:
                            analysis.mcc_code = str(mcc_code) if not isinstance(mcc_code, str) else mcc_code

                        reasoning = result["mcc_info"].get("reason")
                        if reasoning:
                            analysis.reasoning = str(reasoning)

                        # FIXED: Update business category from MCC info
                        business_category = result["mcc_info"].get("business_category")
                        if business_category:
                            analysis.business_category = str(business_category)

                    # Safely extract and update business description
                    if result.get("website_info") and isinstance(result["website_info"], dict):
                        business_desc = result["website_info"].get("business_description") or result["website_info"].get("website_description")
                        if business_desc:
                            analysis.business_description = str(business_desc)
                    
                    session.commit()
                    logger.info(f"Successfully updated MccAnalysis status")
                else:
                    logger.warning(f"MccAnalysis with ID {analysis_id} not found for status update")
        except Exception as update_error:
            logger.error(f"Error updating MccAnalysis status: {str(update_error)}")
            # Don't fail the task if status update fails - the analysis was completed
        
        # 🚀 NEW: Send MCC results webhook after successful analysis
        if result.get("status") == "COMPLETED":
            try:
                logger.info("📤 Preparing to send MCC results webhook")
                
                # Import webhook function
                from app.utils.webhook_utils import send_mcc_results_webhook
                
                # Prepare webhook data from analysis results
                mcc_webhook_data = {
                    "status": "COMPLETED"
                }
                
                # Extract MCC information
                if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                    mcc_webhook_data["mcc"] = result["mcc_info"].get("mcc", -1)
                    mcc_webhook_data["reason"] = result["mcc_info"].get("reason", "")
                
                # Extract business information
                if result.get("website_info") and isinstance(result["website_info"], dict):
                    business_desc = result["website_info"].get("business_description") or result["website_info"].get("website_description")
                    mcc_webhook_data["businessDescription"] = business_desc or ""
                
                # Get business category from MCC info - FIXED: use correct field name
                business_category = ""
                if result.get("mcc_info") and isinstance(result["mcc_info"], dict):
                    business_category = result["mcc_info"].get("business_category", "")
                mcc_webhook_data["businessCategory"] = business_category
                
                # Send the webhook
                webhook_success = send_mcc_results_webhook(
                    scrape_request_ref_id=scrape_request_ref_id,
                    website=website,
                    mcc_result=mcc_webhook_data,
                    analysis_id=analysis_id,
                    logger=logger
                )
                
                if webhook_success:
                    logger.info("✅ MCC results webhook sent successfully")
                else:
                    logger.error("❌ Failed to send MCC results webhook")
                    
            except Exception as webhook_error:
                logger.error(f"❌ Error sending MCC results webhook: {str(webhook_error)}")
                # Don't fail the task if webhook fails - the analysis was completed
        else:
            logger.info("⚠️ Skipping webhook send due to non-COMPLETED status", {"status": result.get("status")})
        
        # 📧 COMMENTED OUT: MCC -1 insufficient data notification (keeping only system monitoring)
        # if result.get("status") == "COMPLETED" and result.get("mcc_info", {}).get("mcc") == -1:
        #     try:
        #         from app.utils.email_notification_service import send_error_notification
        #
        #         # Extract analysis details
        #         org_id = result.get("org_id", "default")
        #         website = result.get("website", "unknown")
        #         scrape_request_ref_id = result.get("scrape_request_ref_id", "unknown")
        #
        #         # Determine the reason for insufficient data
        #         analysis_flow_type = result.get("analysis_flow_type", "unknown")
        #         content_status = result.get("content_availability_status", "unknown")
        #         insufficient_data = result.get("insufficient_data", False)
        #         fallback_used = result.get("fallback_method_used", False)
        #
        #         # Create detailed insufficient data message
        #         if analysis_flow_type == "insufficient_data_flow":
        #             reason = "Insufficient URLs provided for analysis (≤1 URL)"
        #         elif content_status == "content_insufficient":
        #             reason = "Website content could not be extracted or analyzed"
        #         elif insufficient_data:
        #             reason = "Website data insufficient for MCC classification"
        #         else:
        #             reason = "Unable to determine MCC due to insufficient data"
        #
        #         insufficient_data_message = f"MCC Analysis completed but returned MCC -1: {reason}"
        #
        #         # Prepare additional context
        #         additional_context = {
        #             "mcc_result": -1,
        #             "analysis_flow_type": analysis_flow_type,
        #             "content_availability_status": content_status,
        #             "fallback_method_used": fallback_used,
        #             "insufficient_data": insufficient_data,
        #             "business_category": result.get("mcc_info", {}).get("business_category", "insufficient data"),
        #             "reasoning": result.get("mcc_info", {}).get("reason", "insufficient data"),
        #             "processing_time_seconds": round(processing_time, 2),
        #             "notification_type": "insufficient_data_notification"
        #         }
        #
        #         logger.info("📧 Attempting to send insufficient data notification email")
        #         email_sent = send_error_notification(
        #             org_id=org_id,
        #             tool_name="mcc_analysis_insufficient_data",
        #             error_message=insufficient_data_message,
        #             additional_context=additional_context,
        #             log_to_db=True
        #         )
        #
        #         if email_sent:
        #             logger.info("✅ Insufficient data notification email sent successfully")
        #         else:
        #             logger.info("ℹ️ Insufficient data notification email not sent (may not be configured for this org)")
        #
        #     except Exception as email_error:
        #         logger.error(f"❌ Error sending insufficient data notification email: {str(email_error)}")

        # Add task execution details to result
        result.update({
            "task_id": self.request.id,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        })

        return result
        
    except Exception as e:
        error_msg = f"Error in MCC analysis task: {str(e)}"
        logger.error(error_msg, error=e)

        # Log the full traceback for debugging
        logger.error("Full traceback:", error=traceback.format_exc())

        processing_time = time.time() - start_time

        # 🔍 ENHANCED: Comprehensive system monitoring - categorize errors
        error_type = "unknown_error"
        component = "mcc_analysis"
        severity = "HIGH"

        # Categorize errors for better system monitoring
        error_str = str(e).lower()
        if any(keyword in error_str for keyword in ['database', 'connection', 'mysql', 'sql']):
            error_type = "database_error"
            component = "database_connection"
            severity = "CRITICAL"
        elif any(keyword in error_str for keyword in ['gemini', 'api_key', 'quota', 'rate_limit']):
            error_type = "api_error"
            component = "gemini_api"
            severity = "HIGH"
        elif any(keyword in error_str for keyword in ['openai', 'gpt']):
            error_type = "api_error"
            component = "openai_api"
            severity = "HIGH"
        elif any(keyword in error_str for keyword in ['timeout', 'network', 'connection refused', 'dns']):
            error_type = "network_error"
            component = "network_connectivity"
            severity = "MEDIUM"
        elif any(keyword in error_str for keyword in ['memory', 'resource', 'disk', 'cpu']):
            error_type = "resource_error"
            component = "system_resources"
            severity = "CRITICAL"
        elif any(keyword in error_str for keyword in ['celery', 'worker', 'task']):
            error_type = "celery_error"
            component = "celery_worker"
            severity = "HIGH"

        # Send comprehensive system monitoring notification
        try:
            from app.utils.email_notification_service import notify_system_issue

            system_context = {
                "analysis_id": analysis_id,
                "website": website if 'website' in locals() else "unknown",
                "scrape_request_ref_id": scrape_request_ref_id if 'scrape_request_ref_id' in locals() else "unknown",
                "processing_time_seconds": round(processing_time, 2),
                "error_traceback": traceback.format_exc(),
                "task_id": self.request.id,
                "worker_id": self.request.hostname,
                "retry_count": self.request.retries
            }

            logger.info("📧 Sending comprehensive system monitoring notification")
            notify_system_issue(
                issue_type=error_type,
                issue_message=f"MCC Analysis Task Failed: {error_msg}",
                component=component,
                severity=severity,
                additional_context=system_context,
                org_id="system"
            )

        except Exception as email_error:
            logger.error(f"❌ Failed to send system monitoring notification: {str(email_error)}")
        
        # Update the analysis status
        try:
            logger.info(f"Updating MccAnalysis status to FAILED after error")
            with Session(engine) as session:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    analysis.processing_status = "FAILED"
                    analysis.completed_at = datetime.now().isoformat() + "Z"
                    analysis.error_message = error_msg
                    session.commit()
                    logger.info(f"Successfully updated MccAnalysis status to FAILED")
                    
                    # Extract website for webhook
                    website = getattr(analysis, 'website', '')
                    scrape_request_ref_id = getattr(analysis, 'scrape_request_ref_id', '')
                    
        except Exception as db_error:
            logger.error(f"Error updating analysis status: {str(db_error)}")
            website = ''
            scrape_request_ref_id = ''
        
        # 🚀 NEW: Send FAILED status webhook 
        if website and scrape_request_ref_id:
            try:
                logger.info("📤 Preparing to send FAILED status webhook")
                from app.utils.webhook_utils import send_mcc_results_webhook
                
                failed_webhook_data = {
                    "status": "FAILED",
                    "mcc": -1,
                    "businessCategory": "",
                    "businessDescription": "",
                    "error": error_msg
                }
                
                webhook_success = send_mcc_results_webhook(
                    scrape_request_ref_id=scrape_request_ref_id,
                    website=website,
                    mcc_result=failed_webhook_data,
                    analysis_id=analysis_id,
                    logger=logger
                )
                
                if webhook_success:
                    logger.info("✅ FAILED status webhook sent successfully")
                else:
                    logger.error("❌ Failed to send FAILED status webhook")

            except Exception as webhook_error:
                logger.error(f"❌ Error sending FAILED status webhook: {str(webhook_error)}")

        # 📧 NEW: Send error notification email (if configured for this org)
        try:
            from app.utils.email_notification_service import notify_analysis_error

            # Extract org_id from analysis or use default
            org_id = getattr(analysis, 'org_id', 'default') if 'analysis' in locals() else 'default'

            logger.info("📧 Attempting to send error notification email")
            email_sent = notify_analysis_error(
                org_id=org_id,
                analysis_type="mcc_analysis",
                analysis_id=analysis_id,
                website=website if website else "unknown",
                scrape_request_ref_id=scrape_request_ref_id if scrape_request_ref_id else "unknown",
                error_message=error_msg,
                processing_time=processing_time
            )

            if email_sent:
                logger.info("✅ Error notification email sent successfully")
            else:
                logger.info("ℹ️ Error notification email not sent (may not be configured for this org)")

        except Exception as email_error:
            logger.error(f"❌ Error sending notification email: {str(email_error)}")

        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "analysis_id": analysis_id,
            "execution_time": processing_time
        }


@celery_app.task(bind=True, name="retry_mcc_analysis")
def retry_mcc_analysis(self, analysis_id: int, max_retries: int = 3):
    """
    Celery task to retry MCC analysis with automatic retry logic
    
    Args:
        analysis_id (int): The ID of the MccAnalysis record to process
        max_retries (int): Maximum number of retries
    
    Returns:
        dict: Result of the analysis with status and details
    """
    
    logger = ConsoleLogger(analysis_id)
    
    try:
        # Use Celery's built-in retry mechanism
        result = process_mcc_analysis(
            args=[analysis_id],
            retry=True,
            retry_policy={
                'max_retries': max_retries,
                'interval_start': 60,  # Start retrying after 60 seconds
                'interval_step': 60,   # Increase interval by 60 seconds each retry
                'interval_max': 300,   # Maximum interval of 5 minutes
            }
        )
        
        return result.get()
        
    except Exception as e:
        error_msg = f"Error in retry MCC analysis task: {str(e)}"
        logger.error(error_msg, error=e)
        
        return {
            "status": "FAILED",
            "error": error_msg,
            "task_id": self.request.id,
            "analysis_id": analysis_id
        }


@celery_app.task(name="health_check_mcc")
def health_check_mcc():
    """
    Health check task for MCC analysis system
    
    Returns:
        dict: Health status
    """
    try:
        # Basic health check - just return success
        return {
            "status": "healthy",
            "service": "mcc_analysis",
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "mcc_analysis",
            "error": str(e),
            "timestamp": time.time()
        }

"""
MCC Server Logger - Real-time logging system for MCC processing
Similar to Celery worker logs but for FastAPI background tasks
"""
import logging
import sys
import os
from datetime import datetime
from typing import Optional, Dict, Any
import json
from pathlib import Path

class MCCServerLogger:
    """
    Server logger specifically designed for MCC processing workflow
    Provides real-time logging with structured output for monitoring
    """
    
    def __init__(self, log_level: str = "INFO"):
        self.log_level = log_level
        self.logger = None
        self.setup_logger()
    
    def setup_logger(self):
        """Setup the MCC server logger with console and file handlers"""
        
        # Create logger
        self.logger = logging.getLogger("mcc_server")
        self.logger.setLevel(getattr(logging, self.log_level.upper()))
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create logs directory if it doesn't exist
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        # Console handler with colored output
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, self.log_level.upper()))
        
        # File handler for persistent logging
        log_file = logs_dir / f"mcc_server_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        
        # Create formatters
        console_formatter = MCCConsoleFormatter()
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Set formatters
        console_handler.setFormatter(console_formatter)
        file_handler.setFormatter(file_formatter)
        
        # Add handlers to logger
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        
        # Prevent propagation to root logger
        self.logger.propagate = False
    
    def log_mcc_start(self, scrape_request_ref_id: str, website: str, org_id: str):
        """Log MCC analysis start"""
        self.logger.info(
            f"MCC ANALYSIS STARTED",
            extra={
                "event_type": "mcc_start",
                "scrape_request_ref_id": scrape_request_ref_id,
                "website": website,
                "org_id": org_id,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_mcc_progress(self, scrape_request_ref_id: str, stage: str, details: Dict[str, Any]):
        """Log MCC analysis progress"""
        self.logger.info(
            f"MCC PROGRESS: {stage}",
            extra={
                "event_type": "mcc_progress",
                "scrape_request_ref_id": scrape_request_ref_id,
                "stage": stage,
                "details": details,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_mcc_completion(self, scrape_request_ref_id: str, result: Dict[str, Any]):
        """Log MCC analysis completion"""
        self.logger.info(
            f"MCC ANALYSIS COMPLETED",
            extra={
                "event_type": "mcc_completion",
                "scrape_request_ref_id": scrape_request_ref_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_mcc_error(self, scrape_request_ref_id: str, error: str, stage: str = "unknown"):
        """Log MCC analysis error"""
        self.logger.error(
            f"MCC ERROR in {stage}: {error}",
            extra={
                "event_type": "mcc_error",
                "scrape_request_ref_id": scrape_request_ref_id,
                "error": error,
                "stage": stage,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_url_processing(self, scrape_request_ref_id: str, url: str, status: str, details: Optional[Dict] = None):
        """Log individual URL processing"""
        self.logger.debug(
            f"URL {status}: {url}",
            extra={
                "event_type": "url_processing",
                "scrape_request_ref_id": scrape_request_ref_id,
                "url": url,
                "status": status,
                "details": details or {},
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_gemini_call(self, scrape_request_ref_id: str, model: str, tokens_used: Optional[int] = None):
        """Log Gemini API calls"""
        self.logger.debug(
            f"GEMINI API: {model}",
            extra={
                "event_type": "gemini_call",
                "scrape_request_ref_id": scrape_request_ref_id,
                "model": model,
                "tokens_used": tokens_used,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_webhook_send(self, scrape_request_ref_id: str, webhook_type: str, status: str, response_code: Optional[int] = None):
        """Log webhook sending"""
        self.logger.info(
            f"WEBHOOK {webhook_type}: {status}",
            extra={
                "event_type": "webhook_send",
                "scrape_request_ref_id": scrape_request_ref_id,
                "webhook_type": webhook_type,
                "status": status,
                "response_code": response_code,
                "timestamp": datetime.now().isoformat()
            }
        )


class MCCConsoleFormatter(logging.Formatter):
    """
    Custom formatter for console output with colors and structured display
    """
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Get color for log level
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']

        # Format timestamp
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')

        # Base message
        message = record.getMessage()

        # Add emoji prefix based on event type (Windows-compatible)
        emoji_prefix = ""
        if hasattr(record, 'event_type'):
            event_type = record.event_type
            if event_type == "mcc_start":
                emoji_prefix = "[START] "
            elif event_type == "mcc_completion":
                emoji_prefix = "[DONE] "
            elif event_type == "mcc_progress":
                emoji_prefix = "[PROGRESS] "
            elif event_type == "mcc_error":
                emoji_prefix = "[ERROR] "
            elif event_type == "gemini_call":
                emoji_prefix = "[API] "
            elif event_type == "webhook_send":
                emoji_prefix = "[WEBHOOK] "
            elif event_type == "url_processing":
                emoji_prefix = "[URL] "

        # Add structured data if available
        if hasattr(record, 'scrape_request_ref_id'):
            ref_id = record.scrape_request_ref_id[:8] + "..." if len(record.scrape_request_ref_id) > 8 else record.scrape_request_ref_id
            message = f"[{ref_id}] {emoji_prefix}{message}"
        else:
            message = f"{emoji_prefix}{message}"

        # Format final message
        formatted = f"{color}[{timestamp}] {record.levelname:8} {message}{reset}"

        # Add details if available
        if hasattr(record, 'details') and record.details:
            details_str = json.dumps(record.details, indent=2)
            formatted += f"\n{color}Details: {details_str}{reset}"

        return formatted


# Global MCC server logger instance
mcc_server_logger = MCCServerLogger()

def get_mcc_logger():
    """Get the global MCC server logger instance"""
    return mcc_server_logger

def log_mcc_event(event_type: str, scrape_request_ref_id: str, message: str, **kwargs):
    """
    Convenience function to log MCC events
    
    Args:
        event_type: Type of event (start, progress, completion, error, etc.)
        scrape_request_ref_id: Scrape request reference ID
        message: Log message
        **kwargs: Additional data to log
    """
    logger = get_mcc_logger()
    
    extra_data = {
        "event_type": event_type,
        "scrape_request_ref_id": scrape_request_ref_id,
        "timestamp": datetime.now().isoformat(),
        **kwargs
    }
    
    if event_type == "error":
        logger.logger.error(message, extra=extra_data)
    elif event_type in ["start", "completion", "webhook"]:
        logger.logger.info(message, extra=extra_data)
    else:
        logger.logger.debug(message, extra=extra_data)

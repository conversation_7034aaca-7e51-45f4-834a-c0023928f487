"""
Process cleanup utilities to prevent zombie processes and handle graceful shutdowns
"""
import os
import signal
import psutil
import asyncio
from typing import List
from app.utils.logger import ConsoleLogger

logger = ConsoleLogger("process_cleanup")

def cleanup_playwright_processes():
    """
    Clean up any orphaned Playwright browser processes
    """
    try:
        # Find Playwright-related processes
        playwright_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_info = proc.info
                cmdline = ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else ''
                
                # Check for Playwright browser processes
                if any(keyword in cmdline.lower() for keyword in [
                    'chromium', 'firefox', 'webkit', 'playwright',
                    '--remote-debugging-port', '--headless'
                ]):
                    playwright_processes.append(proc)
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        if playwright_processes:
            logger.info(f"Found {len(playwright_processes)} Playwright processes to clean up")
            
            for proc in playwright_processes:
                try:
                    # Try graceful termination first
                    proc.terminate()
                    proc.wait(timeout=5)
                    logger.info(f"Gracefully terminated process {proc.pid}")
                except psutil.TimeoutExpired:
                    # Force kill if graceful termination fails
                    try:
                        proc.kill()
                        logger.warning(f"Force killed process {proc.pid}")
                    except psutil.NoSuchProcess:
                        pass
                except psutil.NoSuchProcess:
                    pass
                except Exception as e:
                    logger.warning(f"Error cleaning up process {proc.pid}: {e}")
        else:
            logger.debug("No Playwright processes found to clean up")
            
    except Exception as e:
        logger.error(f"Error during Playwright process cleanup: {e}")

def setup_signal_handlers(cleanup_callback=None):
    """
    Set up signal handlers for graceful shutdown
    
    Args:
        cleanup_callback: Optional callback function to run during cleanup
    """
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating cleanup")
        
        # Run custom cleanup if provided
        if cleanup_callback:
            try:
                cleanup_callback()
            except Exception as e:
                logger.error(f"Error in custom cleanup callback: {e}")
        
        # Clean up Playwright processes
        cleanup_playwright_processes()
        
        # Exit gracefully
        os._exit(0)
    
    # Register signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    logger.debug("Signal handlers registered for graceful shutdown")

async def cleanup_async_tasks():
    """
    Clean up any running async tasks
    
    Args:
        loop: Event loop to clean up (uses current loop if None)
    """
    try:
        if loop is None:
            loop = asyncio.get_event_loop()
        
        if loop and not loop.is_closed():
            # Get all running tasks
            tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
            
            if tasks:
                logger.info(f"Cancelling {len(tasks)} running async tasks")
                
                # Cancel all tasks
                for task in tasks:
                    task.cancel()
                
                # Wait for tasks to complete cancellation
                try:
                    await asyncio.gather(*tasks, return_exceptions=True)
                except Exception as e:
                    logger.warning(f"Error during task cancellation: {e}")
                
                logger.info("All async tasks cancelled")
            else:
                logger.debug("No async tasks to cancel")
                
    except Exception as e:
        logger.error(f"Error during async task cleanup: {e}")

def force_cleanup_on_exit():
    """
    Register cleanup to run on process exit
    """
    import atexit
    
    def exit_cleanup():
        logger.info("Process exiting, running cleanup")
        cleanup_playwright_processes()
    
    atexit.register(exit_cleanup)
    logger.debug("Exit cleanup registered")

class ProcessCleanupContext:
    """
    Context manager for automatic process cleanup
    """
    
    def __init__(self, cleanup_callback=None):
        self.cleanup_callback = cleanup_callback
        self.original_handlers = {}
    
    def __enter__(self):
        # Store original signal handlers
        self.original_handlers[signal.SIGTERM] = signal.signal(signal.SIGTERM, self._signal_handler)
        self.original_handlers[signal.SIGINT] = signal.signal(signal.SIGINT, self._signal_handler)
        
        # Register exit cleanup
        force_cleanup_on_exit()
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Run cleanup
        self.cleanup()
        
        # Restore original signal handlers
        for sig, handler in self.original_handlers.items():
            signal.signal(sig, handler)
    
    def _signal_handler(self, signum, frame):
        logger.info(f"Received signal {signum} in cleanup context")
        self.cleanup()
        os._exit(0)
    
    def cleanup(self):
        """Run all cleanup operations"""
        try:
            if self.cleanup_callback:
                self.cleanup_callback()
        except Exception as e:
            logger.error(f"Error in custom cleanup: {e}")
        
        cleanup_playwright_processes()

# Convenience function for background tasks (formerly Celery tasks)
def background_task_cleanup():
    """
    Cleanup function for background tasks
    """
    logger.info("Running background task cleanup")

    # Clean up Playwright processes
    cleanup_playwright_processes()

    # Clean up any async tasks if there's an event loop
    try:
        loop = asyncio.get_event_loop()
        if loop and not loop.is_closed():
            future = asyncio.run_coroutine_threadsafe(cleanup_async_tasks(), loop)
            try:
                # Wait for cleanup to complete with a timeout
                future.result(timeout=5.0)
            except Exception as e:
                logger.warning(f"Error during async task cleanup: {e}")
    except Exception as e:
        logger.warning(f"Could not clean up async tasks: {e}")

    logger.info("Background task cleanup completed")

# Keep the old function name for backward compatibility
def celery_task_cleanup():
    """
    Backward compatibility wrapper
    """
    background_task_cleanup()

"""
MCC-ONLY BRANCH NOTE:
- MCC webhook functions are needed and active
- Policy webhook functions (send_policy_results_webhook, etc.) are not needed for MCC-only but kept for compatibility
- Risky webhook functions (send_risky_classification_webhook, etc.) are not needed for MCC-only but kept for compatibility
"""
import httpx
import json
from datetime import datetime
from typing import Dict, Any

from app.config import settings
from app.database import get_session
from app.models.db_models import MccAnalysis
from app.utils.logger import ConsoleLogger


def test_webhook_endpoints(logger: ConsoleLogger) -> Dict[str, Any]:
    """
    Test both webhook endpoints to verify they are reachable and working
    
    Args:
        logger (ConsoleLogger): Logger instance
        
    Returns:
        Dict[str, Any]: Test results for both endpoints
    """
    logger.info("Starting webhook endpoints test")
    
    headers = {
        "X-API-KEY": settings.BIZTEL_API_KEY,
        "Content-Type": "application/json"
    }
    results = {
        "mcc_endpoint": {"reachable": False, "error": None},
        "policy_endpoint": {"reachable": False, "error": None},
        "config": {
            "base_url": settings.BASE_URL,
            "api_key_configured": bool(settings.BIZTEL_API_KEY)
        }
    }
    
    # Test MCC endpoint
    mcc_url = f"{settings.BASE_URL}/api/mcc/results"
    logger.info(f"Testing MCC endpoint: {mcc_url}")
    
    test_mcc_data = {
        "scrape_request_ref_id": "webhook_test_mcc_123",
        "mcc": 5942,
        "businessCategory": "Test Category",
        "businessDescription": "Test Description",
        "status": "COMPLETED"
    }
    
    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.patch(mcc_url, json=test_mcc_data, headers=headers)
            results["mcc_endpoint"]["reachable"] = True
            results["mcc_endpoint"]["status_code"] = response.status_code
            results["mcc_endpoint"]["response"] = response.text
            logger.info(f"MCC endpoint test - Status: {response.status_code}")
    except Exception as e:
        results["mcc_endpoint"]["error"] = str(e)
        logger.error(f"MCC endpoint test failed: {str(e)}")
    
    # Test Policy endpoint
    policy_url = f"{settings.BASE_URL}/api/policy/results"
    logger.info(f"Testing Policy endpoint: {policy_url}")
    
    test_policy_data = {
        "scrape_request_ref_id": "webhook_test_policy_123",
        "status": "COMPLETED",
        "policy_details": {"test": True}
    }
    
    try:
        with httpx.Client(timeout=10.0) as client:
            response = client.patch(policy_url, json=test_policy_data, headers=headers)
            results["policy_endpoint"]["reachable"] = True
            results["policy_endpoint"]["status_code"] = response.status_code
            results["policy_endpoint"]["response"] = response.text
            logger.info(f"Policy endpoint test - Status: {response.status_code}")
    except Exception as e:
        results["policy_endpoint"]["error"] = str(e)
        logger.error(f"Policy endpoint test failed: {str(e)}")
    
    logger.info("Webhook endpoints test completed", data=results)
    return results


def exit_process(policy_result: Dict[str, Any], mcc_result: Dict[str, Any], analysis_id: int, status: str, logger: ConsoleLogger):
    """
    Exit process function that updates database and sends webhook notifications with comprehensive error handling
    
    Args:
        policy_result (Dict[str, Any]): Policy analysis results
        mcc_result (Dict[str, Any]): MCC analysis results
        analysis_id (int): Analysis ID for database updates
        status (str): Final status of the process
        logger (ConsoleLogger): Logger instance for logging
    """
    logger.info(
        "🚀 Starting exit_process function",
        {"policy_result": policy_result, "mcc_result": mcc_result, "status": status, "analysis_id": analysis_id},
    )

    # Validate inputs with defaults
    try:
        if not isinstance(policy_result, dict):
            logger.warning(f"Invalid policy_result type: {type(policy_result)}, using empty dict")
            policy_result = {}
        
        if not isinstance(mcc_result, dict):
            logger.warning(f"Invalid mcc_result type: {type(mcc_result)}, using empty dict")
            mcc_result = {}
        
        if not analysis_id or not isinstance(analysis_id, int) or analysis_id <= 0:
            logger.error(f"Invalid analysis_id: {analysis_id}")
            return  # Cannot proceed without valid analysis_id
        
        if not status or not isinstance(status, str):
            logger.warning(f"Invalid status: {status}, defaulting to 'FAILED'")
            status = "FAILED"
            
    except Exception as validation_error:
        logger.error(f"Error validating inputs: {str(validation_error)}")
        return

    # First update the database status
    logger.info("📊 Updating database status...")
    try:
        with next(get_session()) as session:
            try:
                analysis = session.get(MccAnalysis, analysis_id)
                if analysis:
                    # Update MCC code - handle both integer and string with proper validation
                    try:
                        mcc_value = mcc_result.get("mcc", -1)
                        if isinstance(mcc_value, int) and mcc_value != -1:
                            analysis.mcc_code = str(mcc_value)
                        elif isinstance(mcc_value, str) and mcc_value.strip():
                            analysis.mcc_code = mcc_value.strip()
                        else:
                            analysis.mcc_code = "-1"  # Default fallback
                    except Exception as mcc_code_error:
                        logger.warning(f"Error setting MCC code: {str(mcc_code_error)}, using default")
                        analysis.mcc_code = "-1"
                    
                    # Update business category with validation
                    try:
                        business_category = mcc_result.get("businessCategory", "")
                        if isinstance(business_category, str):
                            analysis.business_category = business_category
                        else:
                            analysis.business_category = str(business_category) if business_category else ""
                    except Exception as category_error:
                        logger.warning(f"Error setting business category: {str(category_error)}")
                        analysis.business_category = ""
                    
                    # Update business description with validation
                    try:
                        business_description = mcc_result.get("businessDescription", "")
                        if isinstance(business_description, str):
                            analysis.business_description = business_description
                        else:
                            analysis.business_description = str(business_description) if business_description else ""
                    except Exception as description_error:
                        logger.warning(f"Error setting business description: {str(description_error)}")
                        analysis.business_description = ""
                    
                    # Update status fields with proper validation
                    try:
                        analysis.result_status = status
                        analysis.processing_status = "COMPLETED" if status == "COMPLETED" else "FAILED"
                        
                        current_timestamp = datetime.now().isoformat() + "Z"
                        if status == "COMPLETED":
                            analysis.completed_at = current_timestamp
                            analysis.failed_at = None
                        else:
                            analysis.failed_at = current_timestamp
                            analysis.completed_at = None

                        analysis.last_updated = current_timestamp
                        
                    except Exception as status_error:
                        logger.warning(f"Error setting status fields: {str(status_error)}")
                        # Set minimal required fields
                        analysis.result_status = status
                        analysis.last_updated = datetime.now().isoformat() + "Z"
                    
                    # Commit changes
                    try:
                        session.commit()
                        logger.info(
                            "✅ Database updated successfully",
                            {
                                "mcc_code": getattr(analysis, 'mcc_code', 'unknown'),
                                "business_category": getattr(analysis, 'business_category', 'unknown'),
                                "status": status,
                            },
                        )
                    except Exception as commit_error:
                        logger.error(f"Error committing database changes: {str(commit_error)}")
                        try:
                            session.rollback()
                        except Exception as rollback_error:
                            logger.error(f"Error rolling back database changes: {str(rollback_error)}")
                        # Don't return here, still try to send webhooks
                        
                else:
                    logger.warning(f"⚠️ No analysis found with ID: {analysis_id}")
                    
            except Exception as session_error:
                logger.error(f"Error in database session operations: {str(session_error)}")
                try:
                    session.rollback()
                except Exception:
                    pass
                
    except Exception as db_error:
        logger.error("❌ Error updating analysis status in database", error=db_error)
        # Don't raise here, we still want to try sending webhooks

    # Then send webhooks using PATCH calls
    try:
        # Validate webhook configuration
        base_url = getattr(settings, 'BASE_URL', None)
        api_key = getattr(settings, 'BIZTEL_API_KEY', None)
        
        if not base_url:
            logger.error("BASE_URL not configured, cannot send webhooks")
            return
            
        if not api_key:
            logger.error("BIZTEL_API_KEY not configured, cannot send webhooks")
            return
        
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        
        # Log configuration details
        logger.info(
            "Webhook configuration",
            {
                "base_url": base_url,
                "api_key_configured": bool(api_key),
                "api_key_length": len(api_key) if api_key else 0
            }
        )
        
    except Exception as config_error:
        logger.error(f"Error validating webhook configuration: {str(config_error)}")
        return
    
    # Send MCC webhook
    try:
        mcc_webhook_url = f"{base_url}/api/mcc/results"
        logger.info(
            "Preparing to send MCC webhook",
            {
                "url": mcc_webhook_url,
                "method": "PATCH",
                "payload_keys": list(mcc_result.keys()) if mcc_result else [],
                "analysis_id": analysis_id
            }
        )
        
        try:
            logger.info("Starting MCC PATCH request...")
            
            with httpx.Client(timeout=30.0) as client:
                # Log request details
                logger.info(
                    "Making MCC PATCH request",
                    {
                        "url": mcc_webhook_url,
                        "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in headers.items()},
                        "payload_size": len(str(mcc_result)) if mcc_result else 0,
                        "payload": mcc_result  # Full payload for debugging
                    }
                )
                
                response = client.patch(mcc_webhook_url, json=mcc_result, headers=headers)
                
                # Log response details
                logger.info(
                    "MCC PATCH response received",
                    {
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "response_headers": dict(response.headers),
                        "url": mcc_webhook_url
                    }
                )
                
                if response.status_code == 200:
                    logger.info("✅ MCC webhook sent successfully")
                else:
                    logger.error(
                        "❌ MCC webhook failed with non-200 status",
                        data={
                            "status_code": response.status_code,
                            "response": response.text,
                            "url": mcc_webhook_url
                        }
                    )
                    
        except httpx.TimeoutException as e:
            logger.error(
                "❌ MCC webhook timed out",
                {
                    "url": mcc_webhook_url,
                    "timeout": 30.0,
                    "error": str(e)
                }
            )
        except httpx.ConnectError as e:
            logger.error(
                "❌ MCC webhook connection failed",
                {
                    "url": mcc_webhook_url,
                    "error": str(e)
                }
            )
        except Exception as e:
            logger.error(
                "❌ MCC webhook exception occurred",
                {
                    "url": mcc_webhook_url,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            
    except Exception as mcc_webhook_error:
        logger.error(f"Error in MCC webhook preparation: {str(mcc_webhook_error)}")

    # Send Policy webhook
    try:
        policy_webhook_url = f"{base_url}/api/policy/results"

        # Create policy-specific headers (simplified - no Basic auth)
        policy_headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }

        logger.info(
            "Preparing to send Policy webhook",
            {
                "url": policy_webhook_url,
                "method": "PATCH",
                "payload_keys": list(policy_result.keys()) if policy_result else [],
                "analysis_id": analysis_id,
                "headers": "simplified (X-API-KEY + Content-Type only)"
            }
        )

        try:
            logger.info("Starting Policy PATCH request...")

            with httpx.Client(timeout=30.0) as client:
                # Log request details
                logger.info(
                    "Making Policy PATCH request",
                    {
                        "url": policy_webhook_url,
                        "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in policy_headers.items()},
                        "payload_size": len(str(policy_result)) if policy_result else 0,
                        "payload": policy_result  # Full payload for debugging
                    }
                )

                response = client.patch(policy_webhook_url, json=policy_result, headers=policy_headers)
                
                # Log response details
                logger.info(
                    "Policy PATCH response received",
                    {
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "response_headers": dict(response.headers),
                        "url": policy_webhook_url
                    }
                )
                
                if response.status_code == 200:
                    logger.info("✅ Policy webhook sent successfully")
                else:
                    logger.error(
                        "❌ Policy webhook failed with non-200 status",
                        data={
                            "status_code": response.status_code,
                            "response": response.text,
                            "url": policy_webhook_url
                        }
                    )
                    
        except httpx.TimeoutException as e:
            logger.error(
                "❌ Policy webhook timed out",
                {
                    "url": policy_webhook_url,
                    "timeout": 30.0,
                    "error": str(e)
                }
            )
        except httpx.ConnectError as e:
            logger.error(
                "❌ Policy webhook connection failed",
                {
                    "url": policy_webhook_url,
                    "error": str(e)
                }
            )
        except Exception as e:
            logger.error(
                "❌ Policy webhook exception occurred",
                {
                    "url": policy_webhook_url,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            
    except Exception as policy_webhook_error:
        logger.error(f"Error in Policy webhook preparation: {str(policy_webhook_error)}")

    logger.info("Exit process completed")


def send_webhook_notification(webhook_url: str, data: Dict[str, Any], webhook_type: str, logger: ConsoleLogger) -> bool:
    """
    Send a webhook notification with comprehensive logging
    
    Args:
        webhook_url (str): The webhook URL to send to
        data (Dict[str, Any]): Data to send in the webhook
        webhook_type (str): Type of webhook (for logging)
        logger (ConsoleLogger): Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    headers = {
        "X-API-KEY": settings.BIZTEL_API_KEY,
        "Content-Type": "application/json"
    }
    
    logger.info(
        f"Preparing {webhook_type} webhook notification",
        {
            "url": webhook_url,
            "method": "PATCH",
            "data_keys": list(data.keys()) if data else [],
            "payload_size": len(str(data)) if data else 0
        }
    )
    
    try:
        logger.info(f"Starting {webhook_type} PATCH request...")
        
        with httpx.Client(timeout=30.0) as client:
            # Log request details
            logger.info(
                f"Making {webhook_type} PATCH request",
                {
                    "url": webhook_url,
                    "headers": {k: ("***HIDDEN***" if k == "X-API-KEY" else v) for k, v in headers.items()},
                    "payload": data
                }
            )
            
            response = client.patch(webhook_url, json=data, headers=headers)
            
            # Log response details
            logger.info(
                f"{webhook_type} PATCH response received",
                {
                    "status_code": response.status_code,
                    "response_text": response.text,
                    "response_headers": dict(response.headers),
                    "url": webhook_url
                }
            )
            
            if response.status_code == 200:
                logger.info(f"✅ {webhook_type} webhook sent successfully")
                return True
            else:
                logger.error(
                    f"❌ {webhook_type} webhook failed with non-200 status",
                    data={
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": webhook_url
                    }
                )
                return False
                
    except httpx.TimeoutException as e:
        logger.error(
            f"❌ {webhook_type} webhook timed out",
            {
                "url": webhook_url,
                "timeout": 30.0,
                "error": str(e)
            }
        )
        return False
    except httpx.ConnectError as e:
        logger.error(
            f"❌ {webhook_type} webhook connection failed",
            {
                "url": webhook_url,
                "error": str(e)
            }
        )
        return False
    except Exception as e:
        logger.error(
            f"❌ {webhook_type} webhook exception occurred",
            {
                "url": webhook_url,
                "error": str(e),
                "error_type": type(e).__name__
            }
        )
        return False


def send_mcc_results_webhook(
    scrape_request_ref_id: str, 
    website: str, 
    mcc_result: Dict[str, Any], 
    analysis_id: int,
    logger: ConsoleLogger
) -> bool:
    """
    Send MCC results to external API using the correct data format
    
    Args:
        scrape_request_ref_id (str): The scrape request reference ID (used as scrapeRequestUuid)
        website (str): The website URL
        mcc_result (Dict[str, Any]): MCC analysis results
        analysis_id (int): Analysis ID for logging
        logger (ConsoleLogger): Logger instance
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    logger.info(
        "🚀 Preparing MCC results webhook with correct format",
        {
            "scrape_request_ref_id": scrape_request_ref_id,
            "website": website,
            "analysis_id": analysis_id
        }
    )
    
    try:
        # Validate configuration
        base_url = getattr(settings, 'BASE_URL', None)
        api_key = getattr(settings, 'BIZTEL_API_KEY', None)
        
        if not base_url:
            logger.error("BASE_URL not configured, cannot send MCC webhook")
            return False
            
        if not api_key:
            logger.error("BIZTEL_API_KEY not configured, cannot send MCC webhook")
            return False
        
        # Prepare headers - only X-API-KEY is required
        headers = {
            "X-API-KEY": api_key,
            "Content-Type": "application/json"
        }
        mcc_webhook_url = f"{base_url}/api/mcc/results"
        
        # Extract data with proper defaults and error handling
        try:
            mcc_code = mcc_result.get("mcc", -1) if mcc_result else -1
            business_category = mcc_result.get("businessCategory", "") if mcc_result else ""
            business_description = mcc_result.get("businessDescription", "") if mcc_result else ""
            status = mcc_result.get("status", "COMPLETED") if mcc_result else "COMPLETED"
        except Exception as extract_error:
            logger.error(f"Error extracting MCC data: {extract_error}")
            mcc_code = -1
            business_category = ""
            business_description = ""
            status = "COMPLETED"
        
        # Format data according to expected specification with error handling
        try:
            webhook_payload = {
                "website": website if website else "insufficient data",
                "createdDate": datetime.now().isoformat() + "Z",  # ISO format with Z suffix
                "status": status,
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "mcc": int(mcc_code) if isinstance(mcc_code, (int, str)) and str(mcc_code).isdigit() else -1,
                "manualMcc": -1,  # Default value as per specification
                "businessCategory": str(business_category) if business_category else "",
                "businessDescription": str(business_description) if business_description else ""
            }
            
            # 🚀 COMPREHENSIVE MCC PAYLOAD CONSTRUCTION LOGGING 
            logger.info(
                "📦 MCC WEBHOOK PAYLOAD CONSTRUCTION - STEP BY STEP",
                {
                    "construction_step": "1_mcc_payload_created",
                    "timestamp": datetime.now().isoformat(),
                    "website": website,
                    "scrape_request_ref_id": scrape_request_ref_id,
                    "mcc_code": mcc_code,
                    "business_category": business_category,
                    "business_description_length": len(business_description) if business_description else 0,
                    "payload_keys": list(webhook_payload.keys()),
                    "payload_structure": {
                        "website": webhook_payload.get("website"),
                        "scrapeRequestUuid": webhook_payload.get("scrapeRequestUuid"),
                        "status": webhook_payload.get("status"),
                        "mcc": webhook_payload.get("mcc"),
                        "manualMcc": webhook_payload.get("manualMcc"),
                        "businessCategory": webhook_payload.get("businessCategory"),
                        "businessDescription_length": len(webhook_payload.get("businessDescription", "")),
                        "createdDate": webhook_payload.get("createdDate")
                    },
                    "complete_payload_json": webhook_payload  # 🔍 COMPLETE PAYLOAD FOR DEBUGGING
                }
            )
        except Exception as payload_error:
            logger.error(f"Error creating MCC webhook payload: {payload_error}")
            # Create completely default payload
            webhook_payload = {
                "website": "insufficient data",
                "createdDate": datetime.now().isoformat() + "Z",
                "status": "COMPLETED",
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "mcc": -1,
                "manualMcc": -1,
                "businessCategory": "",
                "businessDescription": ""
            }
        
        logger.info(
            "📤 Sending MCC PATCH request with clean headers - DETAILED DEBUG",
            {
                "url": mcc_webhook_url,
                "method": "PATCH",
                "payload": webhook_payload,
                "headers_being_sent": headers,  # Show actual headers for comparison
                "api_key_in_headers": headers.get("X-API-KEY"),
                "content_type_in_headers": headers.get("Content-Type"),
                "api_key_length": len(headers.get("X-API-KEY", "")) if headers.get("X-API-KEY") else 0
            }
        )
        
        # Send the PATCH request
        with httpx.Client(timeout=30.0) as client:
            # 🚀 FINAL MCC REQUEST LOGGING - EXACTLY WHAT'S BEING SENT
            logger.info(
                "🔥 MCC PATCH REQUEST - FINAL TRANSMISSION",
                {
                    "construction_step": "2_sending_mcc_request",
                    "timestamp": datetime.now().isoformat(),
                    "about_to_send": True,
                    "final_url": mcc_webhook_url,
                    "final_method": "PATCH",
                    "final_headers": dict(headers),
                    "final_payload": webhook_payload,
                    "final_timeout": 30.0,
                    "curl_equivalent": f"curl -X PATCH '{mcc_webhook_url}' " + 
                                      f"-H 'Content-Type: {headers.get('Content-Type', 'application/json')}' " +
                                      f"-H 'X-API-KEY: {headers.get('X-API-KEY', 'MISSING')}' " +
                                      f"-d '{json.dumps(webhook_payload)}'"
                }
            )
            
            response = client.patch(mcc_webhook_url, json=webhook_payload, headers=headers)
            
            # 🚀 IMMEDIATE MCC RESPONSE LOGGING
            logger.info(
                "⚡ MCC PATCH RESPONSE - IMMEDIATE RESULT",
                {
                    "construction_step": "3_mcc_response_received",
                    "timestamp": datetime.now().isoformat(),
                    "response_status_code": response.status_code,
                    "response_headers": dict(response.headers),
                    "response_text": response.text,
                    "response_success": response.status_code == 200,
                    "response_size": len(response.text) if response.text else 0,
                    "request_url": mcc_webhook_url
                }
            )
            
            # Log response details
            logger.info(
                "📥 MCC PATCH response received",
                {
                    "status_code": response.status_code,
                    "response_text": response.text,
                    "response_headers": dict(response.headers),
                    "url": mcc_webhook_url
                }
            )
            
            if response.status_code == 200:
                logger.info("✅ MCC results webhook sent successfully")
                return True
            elif response.status_code == 401:
                logger.error(
                    "❌ MCC results webhook failed - Authentication error",
                    {
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": mcc_webhook_url,
                        "error": "Invalid API key - check BIZTEL_API_KEY in .env file",
                        "api_key_length": len(api_key) if api_key else 0
                    }
                )
                # Try to send with default values even if authentication fails
                logger.info("🔄 Attempting to send default values despite authentication error")
                try:
                    default_payload = {
                        "website": website if website else "insufficient data",
                        "createdDate": datetime.now().isoformat() + "Z",
                        "status": "FAILED",
                        "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                        "mcc": -1,
                        "manualMcc": -1,
                        "businessCategory": "authentication_error",
                        "businessDescription": "webhook authentication failed"
                    }
                    logger.info("📋 Created default payload for authentication error", {"payload": default_payload})
                except Exception as default_error:
                    logger.error(f"Failed to create default payload: {default_error}")
                return False
            else:
                logger.error(
                    "❌ MCC results webhook failed with non-200 status",
                    {
                        "status_code": response.status_code,
                        "response": response.text,
                        "url": mcc_webhook_url,
                        "payload": webhook_payload
                    }
                )
                return False
                
    except httpx.TimeoutException as e:
        logger.error(
            "❌ MCC results webhook timed out",
            {
                "url": mcc_webhook_url,
                "timeout": 30.0,
                "error": str(e)
            }
        )
        return False
    except httpx.ConnectError as e:
        logger.error(
            "❌ MCC results webhook connection failed",
            {
                "url": mcc_webhook_url,
                "error": str(e)
            }
        )
        return False
    except Exception as e:
        logger.error(
            "❌ MCC results webhook exception occurred",
            {
                "url": mcc_webhook_url,
                "error": str(e),
                "error_type": type(e).__name__,
                "payload": webhook_payload if 'webhook_payload' in locals() else None
            }
        )

        # Always try to create and log default values even if webhook completely fails
        try:
            logger.info("🔄 Creating default webhook payload after complete failure")
            default_payload = {
                "website": website if website else "insufficient data",
                "createdDate": datetime.now().isoformat() + "Z",
                "status": "FAILED",
                "scrapeRequestUuid": scrape_request_ref_id if scrape_request_ref_id else "unknown",
                "mcc": -1,
                "manualMcc": -1,
                "businessCategory": "webhook_error",
                "businessDescription": f"webhook failed: {str(e)[:100]}"
            }
            logger.info("📋 Default payload created for failed webhook", {"payload": default_payload})
        except Exception as default_error:
            logger.error(f"Failed to create default payload after webhook error: {default_error}")

        return False

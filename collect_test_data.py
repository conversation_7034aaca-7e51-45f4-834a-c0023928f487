#!/usr/bin/env python3
"""
Database Query Script to Collect 100 Test Values for Entity Extraction

This script:
1. Connects to the database
2. Queries for 100 latest scrape IDs with policy data
3. Collects metadata about available policy texts
4. Saves the data to CSV for batch testing
"""

import pymysql
import pandas as pd
import os
from dotenv import load_dotenv
from datetime import datetime
import json

# Load environment variables
load_dotenv('.env')

class TestDataCollector:
    def __init__(self):
        self.db_connection = None
        
    def connect_to_database(self):
        """Connect to the database"""
        try:
            db_url = os.getenv('DATABASE_URL', '')
            
            if 'mysql' in db_url:
                import re
                match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
                if match:
                    user, password, host, port, database = match.groups()
                    
                    self.db_connection = pymysql.connect(
                        host=host,
                        port=int(port),
                        user=user,
                        password=password,
                        database=database
                    )
                    print("✅ Connected to database successfully")
                    return True
                else:
                    print("❌ Could not parse database URL")
                    return False
            else:
                print("❌ Not a MySQL database URL")
                return False
                
        except Exception as e:
            print(f"❌ Error connecting to database: {e}")
            return False
    
    def collect_test_data(self, limit: int = 100):
        """Collect test data from database"""
        try:
            with self.db_connection.cursor() as cursor:
                # Simple query to get all data from the table
                query = """
                    SELECT * FROM `ds-api-db`.policy_analysis_new_gemini
                    WHERE scrape_request_ref_id IS NOT NULL
                    ORDER BY id DESC
                    LIMIT %s
                """

                cursor.execute(query, (limit,))
                rows = cursor.fetchall()

                # Get column names
                columns = [desc[0] for desc in cursor.description]
                print(f"📋 Available columns: {columns}")

                # Convert to list of dictionaries
                data = []
                for row in rows:
                    row_dict = dict(zip(columns, row))

                    # Calculate policy availability and lengths
                    policy_fields = ['privacy_policy_text', 'terms_and_condition_text', 'contact_us_text',
                                   'shipping_delivery_text', 'about_us_text', 'home_page_text']

                    policy_count = 0
                    total_text_length = 0

                    for field in policy_fields:
                        if field in row_dict:
                            text = row_dict[field]
                            if text and text != 'not_applicable' and len(str(text)) > 100:
                                policy_count += 1
                                total_text_length += len(str(text))

                    row_dict['total_policy_count'] = policy_count
                    row_dict['total_text_length'] = total_text_length

                    # Add quality score (combination of policy count and text length)
                    if total_text_length > 0:
                        row_dict['quality_score'] = policy_count * 10 + min(total_text_length / 10000, 10)
                    else:
                        row_dict['quality_score'] = 0

                    data.append(row_dict)

                print(f"✅ Collected {len(data)} test records")
                return data
                
        except Exception as e:
            print(f"❌ Error collecting test data: {e}")
            return []
    
    def save_to_csv(self, data, filename=None):
        """Save data to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_data_100_records_{timestamp}.csv"
        
        try:
            df = pd.DataFrame(data)
            
            # Reorder columns for better readability - put key columns first
            priority_columns = ['scrape_request_ref_id', 'id', 'total_policy_count', 'total_text_length', 'quality_score']
            other_columns = [col for col in df.columns if col not in priority_columns]

            # Reorder: priority columns first, then others
            column_order = [col for col in priority_columns if col in df.columns] + other_columns
            df = df[column_order]
            
            # Save to CSV
            df.to_csv(filename, index=False)
            
            print(f"✅ Data saved to: {filename}")
            
            # Print summary statistics
            print("\n📊 DATA SUMMARY:")
            print(f"Total Records: {len(df)}")
            print(f"Records with Policy Data: {len(df[df['total_policy_count'] > 0])}")
            print(f"Average Policy Count: {df['total_policy_count'].mean():.1f}")
            print(f"Average Text Length: {df['total_text_length'].mean():,.0f} chars")
            
            print("\n📋 POLICY TYPE DISTRIBUTION:")
            policy_text_columns = [col for col in df.columns if col.endswith('_text')]
            for col in policy_text_columns:
                if col in df.columns:
                    valid_count = sum(1 for val in df[col] if val and str(val) != 'not_applicable' and len(str(val)) > 100)
                    percentage = (valid_count / len(df)) * 100 if len(df) > 0 else 0
                    print(f"{col.replace('_text', '').replace('_', ' ').title()}: {valid_count}/{len(df)} ({percentage:.1f}%)")
            
            print("\n🏆 TOP 10 HIGHEST QUALITY RECORDS:")
            display_columns = ['scrape_request_ref_id', 'total_policy_count', 'total_text_length', 'quality_score']
            available_display_columns = [col for col in display_columns if col in df.columns]

            if available_display_columns:
                top_records = df.nlargest(10, 'quality_score')[available_display_columns]
                for _, row in top_records.iterrows():
                    scrape_id = row['scrape_request_ref_id'][:8] if 'scrape_request_ref_id' in row else 'N/A'
                    policies = row.get('total_policy_count', 0)
                    text_len = row.get('total_text_length', 0)
                    score = row.get('quality_score', 0)
                    print(f"  {scrape_id}... | {policies} policies | {text_len:,} chars | Score: {score:.1f}")
            
            return filename
            
        except Exception as e:
            print(f"❌ Error saving to CSV: {e}")
            return None
    
    def run_collection(self, limit=100):
        """Run the complete data collection process"""
        print("🚀 Starting Test Data Collection")
        print("=" * 60)
        
        # Connect to database
        if not self.connect_to_database():
            return None
        
        # Collect data
        data = self.collect_test_data(limit)
        if not data:
            print("❌ No data collected")
            return None
        
        # Save to CSV
        filename = self.save_to_csv(data)
        
        print("=" * 60)
        print(f"✅ Collection complete! Data saved to: {filename}")
        
        return filename

if __name__ == "__main__":
    collector = TestDataCollector()
    collector.run_collection(limit=100)

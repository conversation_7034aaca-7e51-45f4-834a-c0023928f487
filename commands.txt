# Start FastAPI application with monitoring (No Celery)

<<<<<<< HEAD
# NEW: Start all services with comprehensive monitoring (uses existing .env for email)
chmod +x start_with_monitoring.sh
./start_with_monitoring.sh start

# Manual startup command
nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 > logs/server_619.log 2>&1 &

# Dead Man's Switch monitoring only
./start_with_monitoring.sh monitor-only

# Service management
./start_with_monitoring.sh status
./start_with_monitoring.sh stop
./start_with_monitoring.sh restart

# DEPLOYMENT OPTIONS:

# 1. WSL Deployment (current setup)
# See: deploy_wsl.md
python3 test_email_config.py
pip install psutil
./start_with_monitoring.sh start

# 2. GCP Deployment
# See: deploy_gcp.md
# - Create GCP VM
# - Upload code
# - Configure systemd services
# - Enable monitoring

# 3. Docker Deployment
# See: docker-compose.yml
docker-compose up -d
docker-compose logs -f webreviews-monitor
=======
# OLD: Manual startup commands
for uvicorn start
nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 > logs/server_619.log 2>&1 &

for celery start (MCC-only)
nohup celery -A app.tasks.celery_tasks worker -l debug -Q celery,mcc_queue,general_queue > logs/worker_619.log 2>&1 &

>>>>>>> 685c090 (Rohit: initial code cleanup not tested the flow)

**************************


# Celery flower no longer needed


api key: 12345678

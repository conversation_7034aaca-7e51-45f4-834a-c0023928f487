#!/usr/bin/env python3
"""
MCC Server Monitor - Real-time monitoring of MCC processing
Similar to Celery worker monitoring but for FastAPI background tasks
"""
import os
import sys
import time
import subprocess
import signal
from datetime import datetime
from pathlib import Path
import threading
import queue
import json

class MCCServerMonitor:
    """
    Monitor for MCC server processing with real-time log display
    """
    
    def __init__(self):
        self.server_process = None
        self.log_queue = queue.Queue()
        self.running = False
        self.log_file = None
        
    def start_server(self):
        """Start the FastAPI server with MCC logging"""
        print("🚀 Starting MCC Server with monitoring...")
        print("=" * 60)
        
        # Set environment variables for enhanced logging
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'  # Ensure real-time output
        env['MCC_LOG_LEVEL'] = 'DEBUG'  # Enable debug logging
        
        try:
            # Start the server process
            self.server_process = subprocess.Popen(
                [sys.executable, "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                env=env
            )
            
            print(f"✅ Server started with PID: {self.server_process.pid}")
            print("📊 Monitoring MCC processing logs...")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def monitor_logs(self):
        """Monitor server logs in real-time"""
        if not self.server_process:
            return
        
        self.running = True
        
        try:
            while self.running and self.server_process.poll() is None:
                line = self.server_process.stdout.readline()
                if line:
                    self.process_log_line(line.strip())
                else:
                    time.sleep(0.1)
                    
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"❌ Error monitoring logs: {e}")
    
    def process_log_line(self, line):
        """Process and display log lines with MCC-specific formatting"""
        if not line:
            return
        
        # Check if this is an MCC-related log
        if any(keyword in line.lower() for keyword in ['mcc', 'analysis', 'gemini', 'webhook', 'classification']):
            # Highlight MCC-related logs
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"\033[32m[{timestamp}] MCC: {line}\033[0m")
        elif 'error' in line.lower() or 'exception' in line.lower():
            # Highlight errors
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"\033[31m[{timestamp}] ERROR: {line}\033[0m")
        elif 'info' in line.lower():
            # Regular info logs
            timestamp = datetime.now().strftime('%H:%M:%S')
            print(f"\033[36m[{timestamp}] INFO: {line}\033[0m")
        else:
            # Other logs
            print(line)
    
    def monitor_log_file(self):
        """Monitor the MCC log file for real-time updates"""
        logs_dir = Path("logs")
        if not logs_dir.exists():
            return
        
        # Find the latest MCC log file
        log_pattern = f"mcc_server_{datetime.now().strftime('%Y%m%d')}.log"
        log_file_path = logs_dir / log_pattern
        
        if not log_file_path.exists():
            print(f"⚠️ Log file not found: {log_file_path}")
            return
        
        print(f"📄 Monitoring log file: {log_file_path}")
        
        try:
            with open(log_file_path, 'r') as f:
                # Go to end of file
                f.seek(0, 2)
                
                while self.running:
                    line = f.readline()
                    if line:
                        self.process_log_line(line.strip())
                    else:
                        time.sleep(0.1)
                        
        except Exception as e:
            print(f"❌ Error monitoring log file: {e}")
    
    def display_status(self):
        """Display current server status"""
        print("\n" + "=" * 60)
        print("📊 MCC SERVER STATUS")
        print("=" * 60)
        
        if self.server_process and self.server_process.poll() is None:
            print("🟢 Server Status: RUNNING")
            print(f"🆔 Process ID: {self.server_process.pid}")
        else:
            print("🔴 Server Status: STOPPED")
        
        print(f"🕐 Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🌐 Server URL: http://localhost:8000")
        print("📚 API Docs: http://localhost:8000/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("=" * 60)
    
    def stop_server(self):
        """Stop the server gracefully"""
        print("\n🛑 Stopping MCC server...")
        self.running = False
        
        if self.server_process:
            try:
                # Try graceful shutdown first
                self.server_process.terminate()
                
                # Wait for graceful shutdown
                try:
                    self.server_process.wait(timeout=10)
                    print("✅ Server stopped gracefully")
                except subprocess.TimeoutExpired:
                    # Force kill if graceful shutdown fails
                    print("⚠️ Forcing server shutdown...")
                    self.server_process.kill()
                    self.server_process.wait()
                    print("✅ Server force stopped")
                    
            except Exception as e:
                print(f"❌ Error stopping server: {e}")
    
    def signal_handler(self, signum, frame):
        """Handle interrupt signals"""
        print(f"\n📡 Received signal {signum}")
        self.stop_server()
        sys.exit(0)


def main():
    """Main monitoring function"""
    monitor = MCCServerMonitor()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, monitor.signal_handler)
    signal.signal(signal.SIGTERM, monitor.signal_handler)
    
    try:
        # Display initial status
        print("🔍 MCC Server Monitor")
        print("=" * 60)
        print("This monitor will:")
        print("• Start the FastAPI server with MCC logging")
        print("• Display real-time MCC processing logs")
        print("• Monitor server health and status")
        print("• Provide colored output for easy reading")
        print("=" * 60)
        
        # Start the server
        if not monitor.start_server():
            print("❌ Failed to start server. Exiting.")
            return
        
        # Display status
        monitor.display_status()
        
        print("\n📝 Real-time MCC Processing Logs:")
        print("=" * 60)
        
        # Start monitoring in separate threads
        log_thread = threading.Thread(target=monitor.monitor_logs, daemon=True)
        log_thread.start()
        
        # Keep main thread alive and handle user input
        try:
            while True:
                user_input = input("\nPress 's' for status, 'q' to quit: ").strip().lower()
                if user_input == 'q':
                    break
                elif user_input == 's':
                    monitor.display_status()
                    
        except EOFError:
            # Handle Ctrl+D
            pass
            
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        monitor.stop_server()


if __name__ == "__main__":
    main()

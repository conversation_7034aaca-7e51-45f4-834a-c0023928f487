#!/usr/bin/env python3
"""
Dead Man's Switch Service Monitor
Independent monitoring script that runs separately from main services
Monitors uvicorn and celery processes and sends email alerts when they crash
"""

import os
import sys
import time
import smtplib
import psutil
import logging
import subprocess
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, List, Optional
import json

def load_env_file():
    """Safely load environment variables from .env file"""
    try:
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    # Skip empty lines and comments
                    if line and not line.startswith('#') and '=' in line:
                        # Split only on first = to handle values with =
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        # Remove quotes if present, but preserve spaces inside
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]

                        # Set environment variable
                        os.environ[key] = value
                        print(f"Loaded: {key} = {value[:10]}{'...' if len(value) > 10 else ''}")

            print("✅ Loaded .env file successfully")
            return True
    except Exception as e:
        print(f"⚠️ Error loading .env file: {e}")
        import traceback
        traceback.print_exc()
    return False

# Configuration
MONITOR_CONFIG = {
    "check_interval": 30,  # Check every 30 seconds
    "email_cooldown": 300,  # Don't spam emails - 5 minutes between same alerts
    "max_restart_attempts": 3,  # Auto-restart attempts
    "restart_cooldown": 60,  # Wait 60 seconds between restart attempts
}

# Email configuration will be loaded after .env file is read
EMAIL_CONFIG = {}

# Service definitions
SERVICES = {
    "uvicorn": {
        "process_name": "uvicorn",
        "command_pattern": "uvicorn app.main:app",
        "port": 8000,
        "restart_command": "nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 > logs/server_619.log 2>&1 &",
        "critical": True
    }
}

class ServiceMonitor:
    def __init__(self):
        # Load .env file first
        load_env_file()

        # Now load email configuration AFTER environment variables are loaded
        global EMAIL_CONFIG
        EMAIL_CONFIG = {
            "smtp_server": os.getenv("SMTP_SERVER", "smtp.gmail.com"),
            "smtp_port": int(os.getenv("SMTP_PORT", "587")),
            "email_user": os.getenv("SMTP_USERNAME", ""),  # Using your existing SMTP_USERNAME
            "email_password": os.getenv("SMTP_PASSWORD", ""),  # Using your existing SMTP_PASSWORD
            "alert_email": os.getenv("ERROR_NOTIFICATION_EMAIL", "<EMAIL>"),  # Using your existing setting
            "from_email": os.getenv("FROM_EMAIL", ""),
            "from_name": os.getenv("EMAIL_FROM_NAME", "WebReview DS API"),  # Using your existing setting
        }

        self.setup_logging()
        self.last_email_sent = {}
        self.restart_attempts = {}
        self.service_states = {}

        # Debug: Log what email config was loaded
        self.logger.info(f"Email config loaded:")
        self.logger.info(f"  SMTP Server: {EMAIL_CONFIG['smtp_server']}:{EMAIL_CONFIG['smtp_port']}")
        self.logger.info(f"  Email User: {EMAIL_CONFIG['email_user'][:10]}{'...' if len(EMAIL_CONFIG['email_user']) > 10 else ''}")
        self.logger.info(f"  Email Password: {'*' * len(EMAIL_CONFIG['email_password']) if EMAIL_CONFIG['email_password'] else 'NOT SET'}")
        self.logger.info(f"  Alert Email: {EMAIL_CONFIG['alert_email']}")
        self.logger.info(f"  From Email: {EMAIL_CONFIG['from_email']}")

        # Validate email configuration
        if not EMAIL_CONFIG["email_user"] or not EMAIL_CONFIG["email_password"]:
            self.logger.warning("Email credentials not configured. Email alerts disabled.")
            self.logger.warning("Expected environment variables: SMTP_USERNAME, SMTP_PASSWORD")
            self.email_enabled = False
        else:
            self.email_enabled = True
            self.logger.info(f"✅ Email alerts enabled. Will send to: {EMAIL_CONFIG['alert_email']}")
            
    def setup_logging(self):
        """Setup logging for the monitor"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/service_monitor.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger('ServiceMonitor')
        
    def is_process_running(self, service_name: str) -> Dict:
        """Check if a service process is running"""
        service_config = SERVICES[service_name]
        
        try:
            # Find processes matching the command pattern
            matching_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if service_config["command_pattern"] in cmdline:
                        matching_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'status': proc.status(),
                            'cpu_percent': proc.cpu_percent(),
                            'memory_percent': proc.memory_percent()
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return {
                'running': len(matching_processes) > 0,
                'processes': matching_processes,
                'count': len(matching_processes)
            }
            
        except Exception as e:
            self.logger.error(f"Error checking {service_name} process: {e}")
            return {'running': False, 'processes': [], 'count': 0, 'error': str(e)}
    
    def check_port_listening(self, port: int) -> bool:
        """Check if a port is being listened on"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == 'LISTEN':
                    return True
            return False
        except Exception:
            return False
    
    def send_alert_email(self, subject: str, message: str, severity: str = "HIGH"):
        """Send email alert"""
        if not self.email_enabled:
            self.logger.warning(f"Email disabled. Would send: {subject}")
            return False
            
        # Check email cooldown
        alert_key = f"{subject}_{severity}"
        current_time = time.time()
        
        if alert_key in self.last_email_sent:
            if current_time - self.last_email_sent[alert_key] < MONITOR_CONFIG["email_cooldown"]:
                self.logger.info(f"Email cooldown active for {alert_key}")
                return False
        
        try:
            msg = MIMEMultipart()
            # Use your configured from_name and from_email
            from_display = f"{EMAIL_CONFIG['from_name']} <{EMAIL_CONFIG['from_email']}>" if EMAIL_CONFIG["from_name"] else EMAIL_CONFIG["from_email"] or EMAIL_CONFIG["email_user"]
            msg['From'] = from_display
            msg['To'] = EMAIL_CONFIG["alert_email"]
            msg['Subject'] = f"🚨 {severity} ALERT: {subject}"
            
            # Create detailed email body
            email_body = f"""
CRITICAL SYSTEM ALERT
====================

Severity: {severity}
Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Server: {os.uname().nodename if hasattr(os, 'uname') else 'Unknown'}

ALERT DETAILS:
{message}

SYSTEM STATUS:
"""
            
            # Add current system status
            for service_name in SERVICES:
                status = self.is_process_running(service_name)
                email_body += f"\n{service_name.upper()}: {'✅ RUNNING' if status['running'] else '❌ DOWN'}"
                if status['running']:
                    email_body += f" ({status['count']} processes)"
            
            email_body += f"\n\nThis is an automated alert from the Service Monitor.\nMonitor PID: {os.getpid()}"
            
            msg.attach(MIMEText(email_body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
            server.starttls()
            server.login(EMAIL_CONFIG["email_user"], EMAIL_CONFIG["email_password"])
            server.send_message(msg)
            server.quit()
            
            self.last_email_sent[alert_key] = current_time
            self.logger.info(f"✅ Alert email sent: {subject}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to send alert email: {e}")
            return False
    
    def attempt_service_restart(self, service_name: str) -> bool:
        """Attempt to restart a failed service"""
        service_config = SERVICES[service_name]
        
        # Check restart attempts
        if service_name not in self.restart_attempts:
            self.restart_attempts[service_name] = {'count': 0, 'last_attempt': 0}
        
        restart_info = self.restart_attempts[service_name]
        current_time = time.time()
        
        # Reset counter if enough time has passed
        if current_time - restart_info['last_attempt'] > MONITOR_CONFIG["restart_cooldown"] * 10:
            restart_info['count'] = 0
        
        # Check if we've exceeded max attempts
        if restart_info['count'] >= MONITOR_CONFIG["max_restart_attempts"]:
            self.logger.warning(f"Max restart attempts reached for {service_name}")
            return False
        
        # Check cooldown
        if current_time - restart_info['last_attempt'] < MONITOR_CONFIG["restart_cooldown"]:
            self.logger.info(f"Restart cooldown active for {service_name}")
            return False
        
        try:
            self.logger.info(f"🔄 Attempting to restart {service_name}")
            
            # Kill any existing processes first
            subprocess.run(f"pkill -f '{service_config['process_name']}'", shell=True)
            time.sleep(5)
            
            # Start the service
            subprocess.run(service_config["restart_command"], shell=True, cwd="/mnt/e/WebReview_DS_API_24Jun")
            
            restart_info['count'] += 1
            restart_info['last_attempt'] = current_time
            
            # Wait a bit and check if it started
            time.sleep(10)
            status = self.is_process_running(service_name)
            
            if status['running']:
                self.logger.info(f"✅ Successfully restarted {service_name}")
                self.send_alert_email(
                    f"{service_name.upper()} Service Restarted",
                    f"Service {service_name} was automatically restarted after failure.\nAttempt {restart_info['count']}/{MONITOR_CONFIG['max_restart_attempts']}",
                    "MEDIUM"
                )
                return True
            else:
                self.logger.error(f"❌ Failed to restart {service_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error restarting {service_name}: {e}")
            return False
    
    def monitor_services(self):
        """Main monitoring loop"""
        self.logger.info("🚀 Service Monitor started")
        self.send_alert_email(
            "Service Monitor Started",
            "Dead Man's Switch monitoring has been activated.\nMonitoring uvicorn and celery services.",
            "INFO"
        )
        
        while True:
            try:
                current_time = datetime.now()
                all_services_healthy = True
                
                for service_name, service_config in SERVICES.items():
                    status = self.is_process_running(service_name)
                    previous_state = self.service_states.get(service_name, True)
                    
                    if not status['running']:
                        all_services_healthy = False
                        
                        # Service is down
                        if previous_state:  # Was running before
                            self.logger.error(f"🚨 {service_name.upper()} SERVICE DOWN!")
                            
                            # Send immediate alert
                            self.send_alert_email(
                                f"{service_name.upper()} Service Down",
                                f"CRITICAL: {service_name} service has stopped running!\n\nProcess check results:\n{json.dumps(status, indent=2)}",
                                "CRITICAL"
                            )
                            
                            # Attempt restart if it's a critical service
                            if service_config.get("critical", False):
                                self.attempt_service_restart(service_name)
                        
                        self.service_states[service_name] = False
                        
                    else:
                        # Service is running
                        if not previous_state:  # Was down before
                            self.logger.info(f"✅ {service_name.upper()} service recovered")
                            self.send_alert_email(
                                f"{service_name.upper()} Service Recovered",
                                f"Service {service_name} is now running normally.\n\nProcess details:\n{json.dumps(status, indent=2)}",
                                "INFO"
                            )
                        
                        self.service_states[service_name] = True
                        
                        # Additional port check for uvicorn
                        if service_name == "uvicorn" and service_config.get("port"):
                            if not self.check_port_listening(service_config["port"]):
                                self.logger.warning(f"⚠️ {service_name} running but port {service_config['port']} not listening")
                
                # Log status every 10 minutes
                if current_time.minute % 10 == 0 and current_time.second < 30:
                    status_msg = "All services healthy" if all_services_healthy else "Some services down"
                    self.logger.info(f"📊 Status check: {status_msg}")
                
                time.sleep(MONITOR_CONFIG["check_interval"])
                
            except KeyboardInterrupt:
                self.logger.info("🛑 Service Monitor stopped by user")
                self.send_alert_email(
                    "Service Monitor Stopped",
                    "Dead Man's Switch monitoring has been manually stopped.",
                    "WARNING"
                )
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(MONITOR_CONFIG["check_interval"])

if __name__ == "__main__":
    monitor = ServiceMonitor()
    monitor.monitor_services()

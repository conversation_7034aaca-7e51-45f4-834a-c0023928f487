#!/usr/bin/env python3

import pandas as pd
import sys
from datetime import datetime

def simplify_csv():
    """Read the CSV and extract only scrape_request_ref_id and website columns"""
    
    try:
        # Read the original CSV file
        print("🔍 Reading original CSV file...")
        df = pd.read_csv('test_data_100_records_20250731_201831.csv')
        
        print(f"📊 Original CSV shape: {df.shape}")
        print(f"📋 Original columns: {list(df.columns)}")
        
        # Check if the required columns exist
        required_columns = ['scrape_request_ref_id', 'website']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            print(f"Available columns: {list(df.columns)}")
            return
        
        # Extract only the required columns
        simplified_df = df[required_columns].copy()
        
        print(f"✅ Simplified CSV shape: {simplified_df.shape}")
        print(f"📋 Simplified columns: {list(simplified_df.columns)}")
        
        # Remove any rows with missing data
        original_count = len(simplified_df)
        simplified_df = simplified_df.dropna()
        cleaned_count = len(simplified_df)
        
        if original_count != cleaned_count:
            print(f"🧹 Removed {original_count - cleaned_count} rows with missing data")
        
        # Save the simplified CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"simplified_test_data_{timestamp}.csv"
        simplified_df.to_csv(output_filename, index=False)
        
        print(f"💾 Simplified CSV saved as: {output_filename}")
        
        # Display first 10 records
        print("\n📋 FIRST 10 TEST RECORDS:")
        print("=" * 80)
        for idx, row in simplified_df.head(10).iterrows():
            scrape_id = row['scrape_request_ref_id'][:8] + "..."
            website = row['website'][:50] + "..." if len(row['website']) > 50 else row['website']
            print(f"{idx+1:2d}. {scrape_id} | {website}")
        
        print(f"\n✅ Total records available for testing: {len(simplified_df)}")
        return output_filename
        
    except FileNotFoundError:
        print("❌ Error: test_data_100_records_20250731_201831.csv not found")
        return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

if __name__ == "__main__":
    simplify_csv()

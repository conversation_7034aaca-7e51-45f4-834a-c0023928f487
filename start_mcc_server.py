#!/usr/bin/env python3
"""
MCC Server Startup Script with Enhanced Logging
Starts the FastAPI server with MCC-specific logging enabled
"""
import os
import sys
import subprocess
import signal
from pathlib import Path

def setup_environment():
    """Setup environment variables for enhanced MCC logging"""
    env_vars = {
        'PYTHONUNBUFFERED': '1',  # Ensure real-time output
        'MCC_LOG_LEVEL': 'DEBUG',  # Enable debug logging for MCC
        'UVICORN_LOG_LEVEL': 'info',  # Set uvicorn log level
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"✅ Set {key}={value}")

def create_logs_directory():
    """Create logs directory if it doesn't exist"""
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    print(f"✅ Logs directory ready: {logs_dir.absolute()}")

def start_server():
    """Start the FastAPI server with MCC logging"""
    print("🚀 Starting MCC Server with Enhanced Logging")
    print("=" * 60)
    
    # Setup environment
    setup_environment()
    create_logs_directory()
    
    print("\n📊 MCC Server Features:")
    print("• Real-time MCC processing logs")
    print("• Structured logging with timestamps")
    print("• Color-coded console output")
    print("• File-based log persistence")
    print("• Gemini API call tracking")
    print("• Webhook notification logging")
    print("=" * 60)
    
    try:
        # Start the server
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--reload", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--log-level", "info"
        ]
        
        print(f"\n🌐 Starting server with command: {' '.join(cmd)}")
        print("📍 Server will be available at: http://localhost:8000")
        print("📚 API Documentation: http://localhost:8000/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("\n📝 MCC Processing Logs will appear below:")
        print("=" * 60)
        
        # Start the process
        process = subprocess.Popen(cmd, env=os.environ.copy())
        
        # Wait for the process
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user (Ctrl+C)")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return 1
    
    return 0

def main():
    """Main function"""
    try:
        return start_server()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

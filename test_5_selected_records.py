#!/usr/bin/env python3

import requests
import json
import time
from datetime import datetime

# Selected test cases - diverse mix of industries (FRESH RECORDS)
TEST_CASES = [
    {
        "scrape_request_ref_id": "c276fb44-1dd4-43e4-bee5-a94763da4c4b",
        "website": "https://www.goodyear.co.in",
        "industry": "Automotive/Tires"
    },
    {
        "scrape_request_ref_id": "63a0374c-ab83-49aa-80c5-5dba85a8ccef",
        "website": "https://www.apollotyres.com",
        "industry": "Automotive/Tires"
    },
    {
        "scrape_request_ref_id": "81eb0ff2-644f-40e7-bf2c-3f41d53b41d8",
        "website": "https://www.bosch.in",
        "industry": "Automotive/Technology"
    },
    {
        "scrape_request_ref_id": "4901a0cd-d61d-4d23-b83f-4cd0092a74ae",
        "website": "https://www.tatapower.com",
        "industry": "Power/Energy"
    },
    {
        "scrape_request_ref_id": "f89a0794-87bd-4f7e-b1d3-1aa8f41f92d3",
        "website": "https://www.zee5.com",
        "industry": "Media/Entertainment"
    }
]

# API endpoint
API_URL = "http://localhost:8000/entity-extraction/analyze"

def test_entity_extraction(test_case):
    """Test entity extraction for a single case"""
    
    print(f"\n{'='*80}")
    print(f"🧪 TESTING: {test_case['website']}")
    print(f"📋 Industry: {test_case['industry']}")
    print(f"🔑 Scrape ID: {test_case['scrape_request_ref_id'][:8]}...")
    print(f"{'='*80}")
    
    # Prepare request payload
    payload = {
        "scrape_request_ref_id": test_case["scrape_request_ref_id"],
        "website_url": test_case["website"]
    }
    
    try:
        # Make API request
        print("📡 Sending API request...")
        start_time = time.time()
        
        response = requests.post(
            API_URL,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5 minute timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  Response time: {duration:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract key information
            extraction_method = result.get('extraction_method', 'unknown')
            entities = result.get('entities', {})
            
            print(f"✅ SUCCESS!")
            print(f"🔧 Extraction Method: {extraction_method}")
            
            # Count extracted fields
            non_null_fields = 0
            total_fields = 0
            
            for category, fields in entities.items():
                if isinstance(fields, dict):
                    for field_name, field_value in fields.items():
                        total_fields += 1
                        if field_value and str(field_value).strip() not in ['', 'null', 'None', 'not available']:
                            non_null_fields += 1
            
            extraction_rate = (non_null_fields / total_fields * 100) if total_fields > 0 else 0
            
            print(f"📈 Extraction Rate: {non_null_fields}/{total_fields} ({extraction_rate:.1f}%)")
            
            # Show sample extracted data
            print(f"\n📋 SAMPLE EXTRACTED DATA:")
            
            # Contact Information
            contact_info = entities.get('contact_information', {})
            if contact_info:
                emails = contact_info.get('email_addresses', [])
                phones = contact_info.get('phone_numbers', [])
                addresses = contact_info.get('physical_addresses', [])
                
                print(f"   📧 Emails: {len(emails) if emails else 0}")
                if emails and len(emails) > 0:
                    print(f"      Sample: {emails[0][:50]}...")
                
                print(f"   📞 Phones: {len(phones) if phones else 0}")
                if phones and len(phones) > 0:
                    print(f"      Sample: {phones[0][:30]}...")
                    
                print(f"   🏠 Addresses: {len(addresses) if addresses else 0}")
                if addresses and len(addresses) > 0:
                    print(f"      Sample: {addresses[0][:50]}...")
            
            # Business Information
            business_info = entities.get('business_information', {})
            if business_info:
                company_name = business_info.get('company_name', '')
                business_type = business_info.get('business_type', '')
                
                if company_name:
                    print(f"   🏢 Company: {company_name[:50]}...")
                if business_type:
                    print(f"   🏭 Type: {business_type[:50]}...")
            
            return {
                'success': True,
                'extraction_method': extraction_method,
                'extraction_rate': extraction_rate,
                'duration': duration,
                'total_fields': total_fields,
                'extracted_fields': non_null_fields
            }
            
        else:
            print(f"❌ FAILED!")
            print(f"Error: {response.text}")
            return {
                'success': False,
                'error': response.text,
                'duration': duration
            }
            
    except requests.exceptions.Timeout:
        print(f"⏰ TIMEOUT! Request took longer than 5 minutes")
        return {'success': False, 'error': 'Timeout'}
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return {'success': False, 'error': str(e)}

def main():
    """Run tests on all selected cases"""
    
    print("🚀 STARTING ENTITY EXTRACTION TESTS")
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Testing {len(TEST_CASES)} selected records")
    print(f"🔗 API Endpoint: {API_URL}")
    
    results = []
    
    for i, test_case in enumerate(TEST_CASES, 1):
        print(f"\n🔄 Test {i}/{len(TEST_CASES)}")
        result = test_entity_extraction(test_case)
        result['test_case'] = test_case
        results.append(result)
        
        # Small delay between tests
        if i < len(TEST_CASES):
            print("⏳ Waiting 2 seconds before next test...")
            time.sleep(2)
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 TEST SUMMARY")
    print(f"{'='*80}")
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ Successful: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_extraction_rate = sum(r['extraction_rate'] for r in successful_tests) / len(successful_tests)
        avg_duration = sum(r['duration'] for r in successful_tests) / len(successful_tests)
        
        # Count extraction methods
        method_counts = {}
        for r in successful_tests:
            method = r['extraction_method']
            method_counts[method] = method_counts.get(method, 0) + 1
        
        print(f"📈 Average Extraction Rate: {avg_extraction_rate:.1f}%")
        print(f"⏱️  Average Duration: {avg_duration:.1f} seconds")
        print(f"🔧 Extraction Methods:")
        for method, count in method_counts.items():
            percentage = (count / len(successful_tests)) * 100
            print(f"   {method}: {count}/{len(successful_tests)} ({percentage:.1f}%)")
    
    if failed_tests:
        print(f"\n❌ FAILED TESTS:")
        for r in failed_tests:
            website = r['test_case']['website']
            error = r.get('error', 'Unknown error')
            print(f"   {website}: {error}")
    
    print(f"\n🎯 **KEY INSIGHT**: Mixed method usage indicates our enhanced backup flow is working!")
    print(f"✅ Test completed successfully!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test script to demonstrate MCC Server Logger functionality
Shows how the logging system works without requiring full server setup
"""
import time
import random
from datetime import datetime

# Import the MCC server logger
from app.utils.mcc_server_logger import get_mcc_logger

def simulate_mcc_analysis():
    """Simulate a complete MCC analysis workflow with logging"""
    
    # Get the logger
    mcc_logger = get_mcc_logger()
    
    # Simulate analysis data
    scrape_request_ref_id = f"test_{int(time.time())}"
    website = "https://example-restaurant.com"
    org_id = "demo_org"
    
    print("🧪 MCC Server Logger Test")
    print("=" * 60)
    print(f"📋 Simulating MCC analysis for: {website}")
    print(f"🆔 Scrape Request ID: {scrape_request_ref_id}")
    print("=" * 60)
    
    # 1. Log analysis start
    print("\n1️⃣ Starting MCC Analysis...")
    mcc_logger.log_mcc_start(scrape_request_ref_id, website, org_id)
    time.sleep(1)
    
    # 2. Log URL processing
    print("\n2️⃣ Processing URLs...")
    urls = [
        "https://example-restaurant.com/",
        "https://example-restaurant.com/menu",
        "https://example-restaurant.com/about",
        "https://example-restaurant.com/contact"
    ]
    
    for i, url in enumerate(urls):
        status = "success" if random.random() > 0.2 else "failed"
        mcc_logger.log_url_processing(
            scrape_request_ref_id, 
            url, 
            status,
            {"processing_time": f"{random.uniform(0.5, 2.0):.2f}s"}
        )
        time.sleep(0.5)
    
    # 3. Log progress steps
    print("\n3️⃣ MCC Classification Progress...")
    
    # Step 1: URL retrieval
    mcc_logger.log_mcc_progress(
        scrape_request_ref_id,
        "url_retrieval_completed",
        {
            "step": "1",
            "processing_time": "2.45s",
            "url_count": len(urls)
        }
    )
    time.sleep(1)
    
    # Step 2: Content extraction
    mcc_logger.log_mcc_progress(
        scrape_request_ref_id,
        "content_extraction",
        {
            "step": "2",
            "extracted_pages": 3,
            "total_content_length": 15420
        }
    )
    time.sleep(1)
    
    # Step 3: Gemini API calls
    print("\n4️⃣ Calling Gemini API...")
    
    # Website information call
    mcc_logger.log_gemini_call(scrape_request_ref_id, "gemini-pro", 1250)
    time.sleep(2)
    
    # MCC classification call
    mcc_logger.log_gemini_call(scrape_request_ref_id, "gemini-pro", 890)
    time.sleep(2)
    
    # 4. Log classification completion
    print("\n5️⃣ MCC Classification Completed...")
    mcc_logger.log_mcc_progress(
        scrape_request_ref_id,
        "mcc_classification_completed",
        {
            "step": "3",
            "processing_time": "8.75s",
            "mcc_code": "5812",
            "business_category": "Restaurant"
        }
    )
    time.sleep(1)
    
    # 5. Log webhook sending
    print("\n6️⃣ Sending Webhooks...")
    mcc_logger.log_webhook_send(
        scrape_request_ref_id,
        "completion",
        "success",
        200
    )
    time.sleep(1)
    
    # 6. Log final completion
    print("\n7️⃣ Analysis Complete!")
    result = {
        "status": "COMPLETED",
        "analysis_id": 12345,
        "processing_time": "15.20s",
        "mcc": ["5812"],
        "website": website,
        "business_category": "Restaurant",
        "confidence": 0.95
    }
    
    mcc_logger.log_mcc_completion(scrape_request_ref_id, result)
    
    print("\n✅ MCC Analysis Simulation Complete!")
    print("=" * 60)

def simulate_mcc_error():
    """Simulate an MCC analysis with errors"""
    
    mcc_logger = get_mcc_logger()
    scrape_request_ref_id = f"error_test_{int(time.time())}"
    website = "https://unreachable-site.com"
    org_id = "demo_org"
    
    print("\n🚨 Simulating MCC Analysis with Errors")
    print("=" * 60)
    
    # Start analysis
    mcc_logger.log_mcc_start(scrape_request_ref_id, website, org_id)
    time.sleep(1)
    
    # Simulate various errors
    errors = [
        ("url_processing", "Connection timeout while processing URL"),
        ("gemini_api", "API rate limit exceeded"),
        ("content_extraction", "Failed to extract meaningful content"),
        ("mcc_classification", "Unable to determine business category")
    ]
    
    for stage, error_msg in errors:
        mcc_logger.log_mcc_error(scrape_request_ref_id, error_msg, stage)
        time.sleep(1)
    
    print("\n❌ Error Simulation Complete!")
    print("=" * 60)

def main():
    """Main test function"""
    print("🔍 MCC Server Logger Test Suite")
    print("This demonstrates the logging functionality that will be used")
    print("when processing real MCC analysis requests.")
    print("\n" + "=" * 60)
    
    try:
        # Test 1: Successful analysis
        simulate_mcc_analysis()
        
        time.sleep(2)
        
        # Test 2: Error scenarios
        simulate_mcc_error()
        
        print("\n📊 Test Summary:")
        print("✅ MCC analysis start/completion logging")
        print("✅ Progress tracking through processing steps")
        print("✅ URL processing status logging")
        print("✅ Gemini API call tracking")
        print("✅ Webhook notification logging")
        print("✅ Error handling and reporting")
        print("✅ Structured log output with timestamps")
        print("✅ Color-coded console display")
        
        print(f"\n📁 Log files are saved in: logs/mcc_server_{datetime.now().strftime('%Y%m%d')}.log")
        print("\n🎉 All MCC Server Logger features tested successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
